import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["searchInput", "categoryButton", "article", "loadMoreButton", "noResults"]
  
  connect() {
    console.log("Blog controller connected")
    this.currentCategory = "all"
    this.visibleArticles = 6
    this.allArticles = [...this.articleTargets]
    
    // Initialize
    this.showInitialArticles()
  }

  // Search functionality
  search(event) {
    const searchTerm = event.target.value.toLowerCase()
    this.filterArticles(searchTerm, this.currentCategory)
  }

  // Category filter functionality
  filterByCategory(event) {
    event.preventDefault()
    const button = event.currentTarget
    const category = button.dataset.category
    
    // Update active button state
    this.categoryButtonTargets.forEach(btn => {
      btn.classList.remove("bg-purple-600", "text-white")
      btn.classList.add("bg-white", "text-gray-600", "border", "border-gray-200")
    })
    
    button.classList.remove("bg-white", "text-gray-600", "border", "border-gray-200")
    button.classList.add("bg-purple-600", "text-white")
    
    // Store current category
    this.currentCategory = category
    
    // Filter articles
    const searchTerm = this.searchInputTarget.value.toLowerCase()
    this.filterArticles(searchTerm, category)
  }

  // Main filter logic
  filterArticles(searchTerm, category) {
    let visibleCount = 0
    let hasResults = false

    this.allArticles.forEach(article => {
      const matchesSearch = this.matchesSearch(article, searchTerm)
      const matchesCategory = this.matchesCategory(article, category)
      
      if (matchesSearch && matchesCategory) {
        if (visibleCount < this.visibleArticles) {
          article.style.display = ""
          article.classList.remove("hidden")
          visibleCount++
        } else {
          article.style.display = "none"
          article.classList.add("hidden")
        }
        hasResults = true
      } else {
        article.style.display = "none"
        article.classList.add("hidden")
      }
    })

    // Show/hide no results message
    if (this.hasNoResultsTarget) {
      if (hasResults) {
        this.noResultsTarget.classList.add("hidden")
      } else {
        this.noResultsTarget.classList.remove("hidden")
      }
    }

    // Show/hide load more button
    const hiddenArticles = this.allArticles.filter(article => {
      const matchesSearch = this.matchesSearch(article, searchTerm)
      const matchesCategory = this.matchesCategory(article, category)
      return matchesSearch && matchesCategory && article.style.display === "none"
    })

    if (hiddenArticles.length > 0) {
      this.loadMoreButtonTarget.classList.remove("hidden")
    } else {
      this.loadMoreButtonTarget.classList.add("hidden")
    }
  }

  // Check if article matches search term
  matchesSearch(article, searchTerm) {
    if (!searchTerm) return true
    
    const title = article.querySelector("h3")?.textContent.toLowerCase() || ""
    const description = article.querySelector("p")?.textContent.toLowerCase() || ""
    const category = article.dataset.category?.toLowerCase() || ""
    const author = article.dataset.author?.toLowerCase() || ""
    
    return title.includes(searchTerm) || 
           description.includes(searchTerm) || 
           category.includes(searchTerm) ||
           author.includes(searchTerm)
  }

  // Check if article matches category
  matchesCategory(article, category) {
    if (category === "all") return true
    return article.dataset.category === category
  }

  // Load more articles
  loadMore(event) {
    event.preventDefault()
    this.visibleArticles += 6
    
    const searchTerm = this.searchInputTarget.value.toLowerCase()
    this.filterArticles(searchTerm, this.currentCategory)
  }

  // Show initial articles
  showInitialArticles() {
    this.allArticles.forEach((article, index) => {
      if (index < this.visibleArticles) {
        article.style.display = ""
        article.classList.remove("hidden")
      } else {
        article.style.display = "none"
        article.classList.add("hidden")
      }
    })
  }

  // Newsletter subscription
  subscribe(event) {
    event.preventDefault()
    const form = event.currentTarget
    const emailInput = form.querySelector('input[type="email"]')
    const email = emailInput.value
    
    if (email && this.validateEmail(email)) {
      // Show success message
      const button = form.querySelector('button[type="submit"]')
      const originalText = button.textContent
      button.textContent = "Subscribed!"
      button.classList.add("bg-green-600")
      
      // Reset after 3 seconds
      setTimeout(() => {
        button.textContent = originalText
        button.classList.remove("bg-green-600")
        emailInput.value = ""
      }, 3000)
    }
  }

  // Email validation
  validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  }

  // Play video
  playVideo(event) {
    const videoContainer = event.currentTarget.closest('.video-container')
    const video = videoContainer.querySelector('video')
    const playButton = videoContainer.querySelector('.play-button')
    
    if (video && playButton) {
      video.play()
      playButton.style.display = 'none'
    }
  }

  // Download PDF
  downloadPDF(event) {
    event.preventDefault()
    const button = event.currentTarget
    const filename = button.dataset.filename || "document.pdf"
    
    // Simulate download
    button.textContent = "Downloading..."
    button.disabled = true
    
    setTimeout(() => {
      button.textContent = "Download"
      button.disabled = false
      // In a real app, you'd trigger the actual download here
      console.log(`Downloading ${filename}`)
    }, 2000)
  }

  // Register for webinar
  registerWebinar(event) {
    event.preventDefault()
    const button = event.currentTarget
    const originalText = button.textContent
    
    button.textContent = "Registered!"
    button.classList.add("bg-green-600")
    button.disabled = true
    
    setTimeout(() => {
      button.textContent = originalText
      button.classList.remove("bg-green-600")
      button.disabled = false
    }, 3000)
  }

  // Interactive calculator
  calculateROI(event) {
    const calculator = event.currentTarget.closest('.roi-calculator')
    const monthlyLinks = parseInt(calculator.querySelector('[data-calculator-target="monthlyLinks"]').value) || 0
    const avgCTR = parseFloat(calculator.querySelector('[data-calculator-target="avgCtr"]').value) || 0
    const resultElement = calculator.querySelector('[data-calculator-target="result"]')
    
    // Simple ROI calculation
    const monthlyClicks = monthlyLinks * (avgCTR / 100)
    const estimatedValue = monthlyClicks * 2.5 // $2.50 per click average
    
    resultElement.textContent = `$${estimatedValue.toLocaleString()}`
  }
}