import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="form-validation"
export default class extends Controller {
  static targets = ["field"]

  connect() {
    this.setupFormValidation()
  }

  setupFormValidation() {
    // Add event listeners for real-time validation
    const form = this.element
    if (form) {
      form.addEventListener("submit", this.handleSubmit.bind(this))
    }
  }

  validateField(event) {
    const field = event.target
    this.clearFieldErrors(field)
    
    const isValid = this.validateSingleField(field)
    if (!isValid) {
      this.showFieldError(field, this.getValidationMessage(field))
    }
    
    return isValid
  }

  validateSingleField(field) {
    // Reset custom validity
    field.setCustomValidity("")
    
    // Check HTML5 validity first
    if (!field.checkValidity()) {
      return false
    }
    
    // Custom validation rules
    switch (field.type) {
      case "email":
        return this.validateEmail(field.value)
      case "password":
        return this.validatePassword(field)
      default:
        return true
    }
  }

  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  validatePassword(field) {
    const password = field.value
    const minLength = 8
    
    if (password.length < minLength) {
      field.setCustomValidity(`Password must be at least ${minLength} characters long`)
      return false
    }
    
    // Check for password confirmation match
    if (field.name === "password_confirmation") {
      const passwordField = field.form.querySelector('input[name="password"]')
      if (passwordField && password !== passwordField.value) {
        field.setCustomValidity("Passwords do not match")
        return false
      }
    }
    
    return true
  }

  getValidationMessage(field) {
    if (field.validationMessage) {
      return field.validationMessage
    }
    
    switch (field.type) {
      case "email":
        return "Please enter a valid email address"
      case "password":
        return "Password is required"
      default:
        return "This field is required"
    }
  }

  showFieldError(field, message) {
    // Add error styling
    field.classList.add("border-red-300", "focus:border-red-500", "focus:ring-red-500")
    field.classList.remove("border-gray-300", "focus:border-blue-500", "focus:ring-blue-500")
    
    // Show error message
    let errorElement = field.parentNode.querySelector(".field-error")
    if (!errorElement) {
      errorElement = document.createElement("p")
      errorElement.className = "field-error mt-1 text-sm text-red-600"
      field.parentNode.appendChild(errorElement)
    }
    errorElement.textContent = message
    
    // Set aria-invalid
    field.setAttribute("aria-invalid", "true")
    field.setAttribute("aria-describedby", errorElement.id || "error-" + field.name)
  }

  clearFieldErrors(field) {
    // Remove error styling
    field.classList.remove("border-red-300", "focus:border-red-500", "focus:ring-red-500")
    field.classList.add("border-gray-300", "focus:border-blue-500", "focus:ring-blue-500")
    
    // Remove error message
    const errorElement = field.parentNode.querySelector(".field-error")
    if (errorElement) {
      errorElement.remove()
    }
    
    // Remove aria-invalid
    field.removeAttribute("aria-invalid")
    field.removeAttribute("aria-describedby")
  }

  handleSubmit(event) {
    const form = event.target
    const fields = form.querySelectorAll("input, select, textarea")
    let isFormValid = true
    
    fields.forEach(field => {
      if (!this.validateSingleField(field)) {
        this.showFieldError(field, this.getValidationMessage(field))
        isFormValid = false
      } else {
        this.clearFieldErrors(field)
      }
    })
    
    if (!isFormValid) {
      event.preventDefault()
      
      // Focus on first invalid field
      const firstInvalidField = form.querySelector(".border-red-300")
      if (firstInvalidField) {
        firstInvalidField.focus()
      }
    }
  }
}