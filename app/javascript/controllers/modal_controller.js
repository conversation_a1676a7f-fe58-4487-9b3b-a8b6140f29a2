import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.element.showModal ? this.element.showModal() : this.element.style.display = "block"
    document.body.style.overflow = "hidden"
  }
  
  disconnect() {
    document.body.style.overflow = "auto"
  }
  
  close() {
    this.element.remove()
  }
  
  closeOnBackdropClick(event) {
    if (event.target === this.element) {
      this.close()
    }
  }
  
  closeOnEscape(event) {
    if (event.key === "Escape") {
      this.close()
    }
  }
  
  stopPropagation(event) {
    event.stopPropagation()
  }
}