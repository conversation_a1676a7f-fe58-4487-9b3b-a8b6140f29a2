import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="settings"
export default class extends Controller {
  static targets = ["navigation", "content"]
  static values = { currentSection: String }

  connect() {
    this.updateActiveNavigation()
  }

  switchSection(event) {
    event.preventDefault()
    
    const section = event.currentTarget.dataset.section
    const url = event.currentTarget.href
    
    // Update URL without full page reload
    history.pushState({}, "", url)
    
    // Update active navigation
    this.currentSectionValue = section
    this.updateActiveNavigation()
    
    // Fetch content via Turbo
    this.loadSectionContent(url)
  }

  async loadSectionContent(url) {
    try {
      const response = await fetch(url, {
        headers: {
          "Accept": "text/html",
          "X-Requested-With": "XMLHttpRequest"
        }
      })
      
      if (response.ok) {
        const html = await response.text()
        const parser = new DOMParser()
        const doc = parser.parseFromString(html, "text/html")
        const newContent = doc.querySelector("[data-settings-target='content']")
        
        if (newContent) {
          this.contentTarget.innerHTML = newContent.innerHTML
        }
      }
    } catch (error) {
      console.error("Failed to load section content:", error)
    }
  }

  updateActiveNavigation() {
    const navItems = this.navigationTarget.querySelectorAll(".settings-nav-item")
    
    navItems.forEach(item => {
      const section = item.dataset.section
      if (section === this.currentSectionValue) {
        item.classList.add("settings-nav-item--active")
        item.setAttribute("aria-current", "page")
      } else {
        item.classList.remove("settings-nav-item--active")
        item.removeAttribute("aria-current")
      }
    })
  }

  confirmDeleteAccount(event) {
    if (confirm("Are you absolutely sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.")) {
      if (confirm("This is your final warning. Type 'DELETE' to confirm account deletion.")) {
        const userInput = prompt("Type 'DELETE' to confirm:")
        if (userInput === "DELETE") {
          // Proceed with account deletion
          console.log("Account deletion confirmed")
          // In real implementation, this would make an API call
        } else {
          alert("Account deletion cancelled - incorrect confirmation.")
        }
      }
    }
  }

  enable2FA(event) {
    // In real implementation, this would open a modal with QR code
    alert("2FA setup modal would open here with QR code and backup codes")
  }

  disable2FA(event) {
    if (confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")) {
      // Make API call to disable 2FA
      console.log("Disabling 2FA")
    }
  }

  regenerateBackupCodes(event) {
    if (confirm("Are you sure you want to regenerate your backup codes? Your current codes will no longer work.")) {
      // Make API call to regenerate backup codes
      console.log("Regenerating backup codes")
    }
  }

  refreshSessions(event) {
    // Refresh the active sessions list
    console.log("Refreshing sessions list")
  }

  revokeSession(event) {
    const sessionId = event.currentTarget.dataset.sessionId
    if (confirm("Are you sure you want to revoke this session?")) {
      // Make API call to revoke session
      console.log("Revoking session:", sessionId)
    }
  }

  revokeAllOtherSessions(event) {
    if (confirm("Are you sure you want to sign out of all other sessions? You'll need to sign in again on those devices.")) {
      // Make API call to revoke all other sessions
      console.log("Revoking all other sessions")
    }
  }

  exportData(event) {
    // Trigger data export
    console.log("Starting data export")
    // In real implementation, this would start a background job
    alert("Data export has been started. You'll receive an email when it's ready for download.")
  }
}