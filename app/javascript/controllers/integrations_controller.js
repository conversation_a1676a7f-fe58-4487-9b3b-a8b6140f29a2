import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="integrations"
export default class extends Controller {
  
  connect(event) {
    console.log("Integrations controller connected")
  }

  toggleSection(event) {
    const checkbox = event.target
    const section = checkbox.dataset.section
    const configDiv = document.getElementById(`${section}-config`)
    
    if (configDiv) {
      if (checkbox.checked) {
        configDiv.classList.remove('hidden')
        // Focus on the first input field
        const firstInput = configDiv.querySelector('input[type="text"], input[type="url"]')
        if (firstInput) {
          setTimeout(() => firstInput.focus(), 100)
        }
      } else {
        configDiv.classList.add('hidden')
        // Clear the input values when disabling
        const inputs = configDiv.querySelectorAll('input[type="text"], input[type="url"]')
        inputs.forEach(input => input.value = '')
      }
    }
  }

  connect(event) {
    event.preventDefault()
    const integration = event.currentTarget.dataset.integration
    
    switch (integration) {
      case 'google_analytics':
        this.connectGoogleAnalytics()
        break
      case 'facebook_pixel':
        this.connectFacebookPixel()
        break
      case 'slack':
        this.connectSlack()
        break
      case 'zapier':
        this.connectZapier()
        break
      default:
        console.log(`Connecting to ${integration}`)
    }
  }

  disconnect(event) {
    event.preventDefault()
    const integration = event.currentTarget.dataset.integration
    
    if (confirm(`Are you sure you want to disconnect ${integration}? This will stop sending data to this service.`)) {
      this.disconnectIntegration(integration)
    }
  }

  configure(event) {
    event.preventDefault()
    const integration = event.currentTarget.dataset.integration
    
    switch (integration) {
      case 'google_analytics':
        this.configureGoogleAnalytics()
        break
      case 'facebook_pixel':
        this.configureFacebookPixel()
        break
      case 'slack':
        this.configureSlack()
        break
      case 'zapier':
        this.configureZapier()
        break
      default:
        console.log(`Configuring ${integration}`)
    }
  }

  connectGoogleAnalytics() {
    const trackingId = prompt("Enter your Google Analytics Tracking ID (G-XXXXXXXXXX or UA-XXXXXXXX-X):")
    if (trackingId) {
      this.saveIntegrationConfig('google_analytics', { tracking_id: trackingId })
    }
  }

  connectFacebookPixel() {
    const pixelId = prompt("Enter your Facebook Pixel ID:")
    if (pixelId) {
      this.saveIntegrationConfig('facebook_pixel', { pixel_id: pixelId })
    }
  }

  connectSlack() {
    const webhookUrl = prompt("Enter your Slack Webhook URL:")
    if (webhookUrl && this.isValidUrl(webhookUrl)) {
      this.saveIntegrationConfig('slack', { webhook_url: webhookUrl })
    } else if (webhookUrl) {
      alert("Please enter a valid webhook URL")
    }
  }

  connectZapier() {
    const webhookUrl = prompt("Enter your Zapier Webhook URL:")
    if (webhookUrl && this.isValidUrl(webhookUrl)) {
      this.saveIntegrationConfig('zapier', { webhook_url: webhookUrl })
    } else if (webhookUrl) {
      alert("Please enter a valid webhook URL")
    }
  }

  configureGoogleAnalytics() {
    alert("Google Analytics configuration panel would open here")
  }

  configureFacebookPixel() {
    alert("Facebook Pixel configuration panel would open here")
  }

  configureSlack() {
    alert("Slack configuration panel would open here")
  }

  configureZapier() {
    alert("Zapier configuration panel would open here")
  }

  async saveIntegrationConfig(integration, config) {
    try {
      const response = await fetch('/integration_settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          integration: integration,
          config: config
        })
      })
      
      if (response.ok) {
        location.reload() // Refresh to show updated state
      } else {
        alert('Failed to save integration settings')
      }
    } catch (error) {
      console.error('Error saving integration:', error)
      alert('Failed to save integration settings')
    }
  }

  async disconnectIntegration(integration) {
    try {
      const response = await fetch('/integration_settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          integration: integration,
          action: 'disconnect'
        })
      })
      
      if (response.ok) {
        location.reload() // Refresh to show updated state
      } else {
        alert('Failed to disconnect integration')
      }
    } catch (error) {
      console.error('Error disconnecting integration:', error)
      alert('Failed to disconnect integration')
    }
  }

  isValidUrl(string) {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }
}