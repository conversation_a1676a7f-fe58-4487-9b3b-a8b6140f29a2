import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="custom-domains"
export default class extends Controller {
  
  async verifyDomain(event) {
    event.preventDefault()
    
    const domainId = event.currentTarget.dataset.domainId
    const button = event.currentTarget
    const originalText = button.textContent
    
    // Update button state
    button.textContent = "Verifying..."
    button.disabled = true
    
    try {
      const response = await fetch(`/custom_domains/${domainId}/verify`, {
        method: 'POST',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        
        if (result.verified) {
          this.showSuccessMessage("Domain verified successfully!")
          // Refresh the domain status
          this.refreshDomainStatus(domainId)
        } else {
          this.showErrorMessage(result.error || "Domain verification failed. Please check your DNS settings.")
          // Reset button
          button.textContent = originalText
          button.disabled = false
        }
      } else {
        throw new Error("Verification request failed")
      }
    } catch (error) {
      console.error('Domain verification error:', error)
      this.showErrorMessage("Verification failed. Please try again.")
      
      // Reset button
      button.textContent = originalText
      button.disabled = false
    }
  }

  async refreshDomainStatus(domainId) {
    try {
      const response = await fetch(`/custom_domains/${domainId}`, {
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (response.ok) {
        const html = await response.text()
        const parser = new DOMParser()
        const doc = parser.parseFromString(html, 'text/html')
        const newDomainElement = doc.querySelector(`#custom-domain-${domainId}`)
        const currentDomainElement = document.querySelector(`#custom-domain-${domainId}`)
        
        if (newDomainElement && currentDomainElement) {
          currentDomainElement.outerHTML = newDomainElement.outerHTML
        }
      }
    } catch (error) {
      console.error('Error refreshing domain status:', error)
    }
  }

  async setPrimaryDomain(event) {
    event.preventDefault()
    
    const domainId = event.currentTarget.dataset.domainId
    const button = event.currentTarget
    const originalText = button.textContent
    
    // Update button state
    button.textContent = "Setting..."
    button.disabled = true
    
    try {
      const response = await fetch(`/custom_domains/${domainId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          custom_domain: {
            is_primary: true
          }
        })
      })
      
      if (response.ok) {
        this.showSuccessMessage("Primary domain updated successfully!")
        // Refresh the entire domains list to update all primary indicators
        this.refreshDomainsList()
      } else {
        throw new Error("Failed to set primary domain")
      }
    } catch (error) {
      console.error('Error setting primary domain:', error)
      this.showErrorMessage("Failed to set as primary domain. Please try again.")
      
      // Reset button
      button.textContent = originalText
      button.disabled = false
    }
  }

  async refreshDomainsList() {
    try {
      const response = await fetch('/custom_domains', {
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (response.ok) {
        const html = await response.text()
        const parser = new DOMParser()
        const doc = parser.parseFromString(html, 'text/html')
        const newDomainsList = doc.querySelector('#custom-domains-list')
        const currentDomainsList = document.querySelector('#custom-domains-list')
        
        if (newDomainsList && currentDomainsList) {
          currentDomainsList.innerHTML = newDomainsList.innerHTML
        }
      }
    } catch (error) {
      console.error('Error refreshing domains list:', error)
    }
  }

  showSuccessMessage(message) {
    this.showMessage(message, 'success')
  }

  showErrorMessage(message) {
    this.showMessage(message, 'error')
  }

  showMessage(message, type) {
    // Create and show a temporary message
    const messageDiv = document.createElement('div')
    const baseClasses = 'fixed top-4 right-4 px-4 py-3 rounded z-50'
    
    if (type === 'success') {
      messageDiv.className = `${baseClasses} bg-green-50 border border-green-200 text-green-700`
    } else {
      messageDiv.className = `${baseClasses} bg-red-50 border border-red-200 text-red-700`
    }
    
    messageDiv.textContent = message
    document.body.appendChild(messageDiv)
    
    // Remove after 4 seconds
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.remove()
      }
    }, 4000)
  }

  copyDnsRecord(event) {
    event.preventDefault()
    
    const text = event.currentTarget.dataset.record
    
    navigator.clipboard.writeText(text).then(() => {
      this.showSuccessMessage('DNS record copied to clipboard!')
    }).catch(err => {
      console.error('Failed to copy DNS record: ', err)
      this.showErrorMessage('Failed to copy DNS record')
    })
  }
}