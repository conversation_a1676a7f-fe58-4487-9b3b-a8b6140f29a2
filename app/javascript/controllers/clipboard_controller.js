import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="clipboard"
export default class extends Controller {
  static targets = ["source", "button"]
  static classes = ["success"]

  copy(event) {
    event.preventDefault()
    
    const text = this.sourceTarget.textContent || this.sourceTarget.value
    
    navigator.clipboard.writeText(text).then(() => {
      this.showCopyFeedback()
    }).catch(err => {
      console.error('Failed to copy text: ', err)
      // Fallback for older browsers
      this.fallbackCopy(text)
    })
  }

  showCopyFeedback() {
    const button = this.hasButtonTarget ? this.buttonTarget : event.currentTarget
    const originalText = button.textContent
    
    // Show success feedback
    button.textContent = "Copied!"
    button.classList.add("bg-green-600", "hover:bg-green-700")
    button.classList.remove("bg-gray-600", "hover:bg-gray-700")
    
    // Add success class if defined
    if (this.hasSuccessClass) {
      button.classList.add(this.successClass)
    }
    
    // Reset after 2 seconds
    setTimeout(() => {
      button.textContent = originalText
      button.classList.remove("bg-green-600", "hover:bg-green-700")
      button.classList.add("bg-gray-600", "hover:bg-gray-700")
      
      if (this.hasSuccessClass) {
        button.classList.remove(this.successClass)
      }
    }, 2000)
  }

  fallbackCopy(text) {
    // Create a temporary textarea for older browsers
    const textArea = document.createElement("textarea")
    textArea.value = text
    textArea.style.position = "fixed"
    textArea.style.left = "-999999px"
    textArea.style.top = "-999999px"
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    
    try {
      document.execCommand('copy')
      this.showCopyFeedback()
    } catch (err) {
      console.error('Fallback copy failed: ', err)
    }
    
    document.body.removeChild(textArea)
  }
}