import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="team-settings"
export default class extends Controller {
  static targets = ["membersList", "inviteModal"]

  inviteMember(event) {
    event.preventDefault()
    
    // In a real implementation, this would open a modal
    const email = prompt("Enter the email address of the person you'd like to invite:")
    
    if (email && this.isValidEmail(email)) {
      this.sendInvitation(email)
    } else if (email) {
      alert("Please enter a valid email address.")
    }
  }

  removeMember(event) {
    event.preventDefault()
    
    const memberId = event.currentTarget.dataset.memberId
    const memberName = event.currentTarget.closest('.border').querySelector('.font-medium').textContent
    
    if (confirm(`Are you sure you want to remove ${memberName} from the team?`)) {
      this.removeMemberFromTeam(memberId, event.currentTarget)
    }
  }

  async sendInvitation(email) {
    try {
      const response = await fetch('/team_settings/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({ email: email })
      })
      
      if (response.ok) {
        alert(`Invitation sent to ${email}`)
        // Optionally refresh the members list
        this.refreshMembersList()
      } else {
        const error = await response.json()
        alert(`Failed to send invitation: ${error.message}`)
      }
    } catch (error) {
      console.error('Error sending invitation:', error)
      alert('Failed to send invitation. Please try again.')
    }
  }

  async removeMemberFromTeam(memberId, buttonElement) {
    try {
      const response = await fetch(`/team_settings/members/${memberId}`, {
        method: 'DELETE',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      
      if (response.ok) {
        // Remove the member row from the UI
        const memberRow = buttonElement.closest('.border')
        memberRow.remove()
        
        // Show success message
        this.showSuccessMessage('Team member removed successfully')
      } else {
        const error = await response.json()
        alert(`Failed to remove member: ${error.message}`)
      }
    } catch (error) {
      console.error('Error removing member:', error)
      alert('Failed to remove member. Please try again.')
    }
  }

  async refreshMembersList() {
    try {
      const response = await fetch('/team_settings', {
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (response.ok) {
        const html = await response.text()
        const parser = new DOMParser()
        const doc = parser.parseFromString(html, 'text/html')
        const newMembersList = doc.querySelector('#team-members-list')
        
        if (newMembersList && this.hasMembersListTarget) {
          this.membersListTarget.innerHTML = newMembersList.innerHTML
        }
      }
    } catch (error) {
      console.error('Error refreshing members list:', error)
    }
  }

  showSuccessMessage(message) {
    // Create and show a temporary success message
    const successDiv = document.createElement('div')
    successDiv.className = 'fixed top-4 right-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded z-50'
    successDiv.textContent = message
    
    document.body.appendChild(successDiv)
    
    // Remove after 3 seconds
    setTimeout(() => {
      successDiv.remove()
    }, 3000)
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
}