import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="toggle"
export default class extends Controller {
  static targets = ["button"]
  static classes = ["on", "off"]
  static values = { enabled: <PERSON><PERSON><PERSON> }

  connect() {
    this.updateToggleState()
  }

  toggle(event) {
    event.preventDefault()
    
    this.enabledValue = !this.enabledValue
    this.updateToggleState()
    
    // Dispatch custom event for other controllers to listen to
    this.dispatch("toggled", { 
      detail: { 
        enabled: this.enabledValue,
        element: this.element 
      } 
    })
  }

  updateToggleState() {
    const button = this.buttonTarget
    const knob = button.querySelector("span")
    
    if (this.enabledValue) {
      // Enabled state
      button.classList.remove("bg-gray-200")
      button.classList.add("bg-blue-600")
      button.setAttribute("aria-pressed", "true")
      
      if (knob) {
        knob.classList.remove("translate-x-0")
        knob.classList.add("translate-x-5")
      }
    } else {
      // Disabled state
      button.classList.remove("bg-blue-600")
      button.classList.add("bg-gray-200")
      button.setAttribute("aria-pressed", "false")
      
      if (knob) {
        knob.classList.remove("translate-x-5")
        knob.classList.add("translate-x-0")
      }
    }
  }

  // Keyboard support
  keydown(event) {
    if (event.key === " " || event.key === "Enter") {
      event.preventDefault()
      this.toggle(event)
    }
  }
}