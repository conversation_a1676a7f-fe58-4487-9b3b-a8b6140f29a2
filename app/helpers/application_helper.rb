require 'redcarpet'

module ApplicationHelper
  include Pagy::Frontend
  def render_markdown(text)
    return '' unless text.present?
    
    # Configure Redcarpet with custom HTML renderer
    renderer = Redcarpet::Render::HTML.new(
      filter_html: false,
      hard_wrap: true,
      link_attributes: { class: 'text-purple-600 hover:text-purple-700 underline', target: '_blank', rel: 'noopener' }
    )
    
    markdown = Redcarpet::Markdown.new(renderer,
      autolink: true,
      tables: true,
      fenced_code_blocks: true,
      disable_indented_code_blocks: true,
      strikethrough: true,
      lax_spacing: true,
      space_after_headers: true,
      superscript: true,
      underline: true,
      highlight: true,
      quote: true,
      footnotes: true
    )
    
    # Render markdown to HTML
    html = markdown.render(text)
    
    # Add Tailwind classes to elements
    html.gsub!('<h1>', '<h1 class="text-3xl font-bold text-gray-900 mt-8 mb-4">')
    html.gsub!('<h2>', '<h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4">')
    html.gsub!('<h3>', '<h3 class="text-xl font-bold text-gray-900 mt-6 mb-3">')
    html.gsub!('<h4>', '<h4 class="text-lg font-bold text-gray-900 mt-4 mb-2">')
    
    html.gsub!('<p>', '<p class="text-gray-600 mb-4 leading-relaxed">')
    html.gsub!('<ul>', '<ul class="list-disc list-inside space-y-2 mb-4 text-gray-600">')
    html.gsub!('<ol>', '<ol class="list-decimal list-inside space-y-2 mb-4 text-gray-600">')
    html.gsub!('<li>', '<li class="ml-4">')
    
    html.gsub!('<blockquote>', '<blockquote class="border-l-4 border-purple-500 pl-4 my-4 italic text-gray-700">')
    html.gsub!('<code>', '<code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">')
    html.gsub!('<pre>', '<pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto my-4">')
    
    html.gsub!('<table>', '<table class="min-w-full divide-y divide-gray-200 my-4">')
    html.gsub!('<thead>', '<thead class="bg-gray-50">')
    html.gsub!('<tbody>', '<tbody class="bg-white divide-y divide-gray-200">')
    html.gsub!('<th>', '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">')
    html.gsub!('<td>', '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">')
    
    html.gsub!('<strong>', '<strong class="font-bold text-gray-900">')
    html.gsub!('<em>', '<em class="italic">')
    
    html.html_safe
  end
  
  # Country flag emoji based on country code
  def country_flag(country_code)
    return '🌍' if country_code.blank?
    
    # Convert country code to flag emoji using regional indicator symbols
    country_code.upcase.chars.map { |char| (char.ord + 127397).chr(Encoding::UTF_8) }.join
  rescue
    '🌍'
  end
  
  # Country name from country code
  def country_name(country_code)
    return 'Unknown' if country_code.blank?
    
    countries = {
      # Africa
      'AO' => 'Angola',
      'BF' => 'Burkina Faso',
      'BI' => 'Burundi',
      'BJ' => 'Benin',
      'BW' => 'Botswana',
      'CD' => 'Democratic Republic of the Congo',
      'CF' => 'Central African Republic',
      'CG' => 'Congo',
      'CI' => 'Ivory Coast',
      'CM' => 'Cameroon',
      'CV' => 'Cape Verde',
      'DJ' => 'Djibouti',
      'DZ' => 'Algeria',
      'EG' => 'Egypt',
      'EH' => 'Western Sahara',
      'ER' => 'Eritrea',
      'ET' => 'Ethiopia',
      'GA' => 'Gabon',
      'GH' => 'Ghana',
      'GM' => 'Gambia',
      'GN' => 'Guinea',
      'GQ' => 'Equatorial Guinea',
      'GW' => 'Guinea-Bissau',
      'KE' => 'Kenya',
      'KM' => 'Comoros',
      'LR' => 'Liberia',
      'LS' => 'Lesotho',
      'LY' => 'Libya',
      'MA' => 'Morocco',
      'MG' => 'Madagascar',
      'ML' => 'Mali',
      'MR' => 'Mauritania',
      'MU' => 'Mauritius',
      'MW' => 'Malawi',
      'MZ' => 'Mozambique',
      'NA' => 'Namibia',
      'NE' => 'Niger',
      'NG' => 'Nigeria',
      'RW' => 'Rwanda',
      'SC' => 'Seychelles',
      'SD' => 'Sudan',
      'SL' => 'Sierra Leone',
      'SN' => 'Senegal',
      'SO' => 'Somalia',
      'SS' => 'South Sudan',
      'ST' => 'Sao Tome and Principe',
      'SZ' => 'Eswatini',
      'TD' => 'Chad',
      'TG' => 'Togo',
      'TN' => 'Tunisia',
      'TZ' => 'Tanzania',
      'UG' => 'Uganda',
      'ZA' => 'South Africa',
      'ZM' => 'Zambia',
      'ZW' => 'Zimbabwe',
      
      # Americas
      'AG' => 'Antigua and Barbuda',
      'AI' => 'Anguilla',
      'AR' => 'Argentina',
      'AW' => 'Aruba',
      'BB' => 'Barbados',
      'BL' => 'Saint Barthélemy',
      'BM' => 'Bermuda',
      'BO' => 'Bolivia',
      'BQ' => 'Caribbean Netherlands',
      'BR' => 'Brazil',
      'BS' => 'Bahamas',
      'BZ' => 'Belize',
      'CA' => 'Canada',
      'CL' => 'Chile',
      'CO' => 'Colombia',
      'CR' => 'Costa Rica',
      'CU' => 'Cuba',
      'CW' => 'Curaçao',
      'DM' => 'Dominica',
      'DO' => 'Dominican Republic',
      'EC' => 'Ecuador',
      'FK' => 'Falkland Islands',
      'GD' => 'Grenada',
      'GF' => 'French Guiana',
      'GL' => 'Greenland',
      'GP' => 'Guadeloupe',
      'GT' => 'Guatemala',
      'GY' => 'Guyana',
      'HN' => 'Honduras',
      'HT' => 'Haiti',
      'JM' => 'Jamaica',
      'KN' => 'Saint Kitts and Nevis',
      'KY' => 'Cayman Islands',
      'LC' => 'Saint Lucia',
      'MF' => 'Saint Martin',
      'MQ' => 'Martinique',
      'MS' => 'Montserrat',
      'MX' => 'Mexico',
      'NI' => 'Nicaragua',
      'PA' => 'Panama',
      'PE' => 'Peru',
      'PM' => 'Saint Pierre and Miquelon',
      'PR' => 'Puerto Rico',
      'PY' => 'Paraguay',
      'SR' => 'Suriname',
      'SV' => 'El Salvador',
      'SX' => 'Sint Maarten',
      'TC' => 'Turks and Caicos Islands',
      'TT' => 'Trinidad and Tobago',
      'US' => 'United States',
      'UY' => 'Uruguay',
      'VC' => 'Saint Vincent and the Grenadines',
      'VE' => 'Venezuela',
      'VG' => 'British Virgin Islands',
      'VI' => 'U.S. Virgin Islands',
      
      # Asia
      'AE' => 'United Arab Emirates',
      'AF' => 'Afghanistan',
      'AM' => 'Armenia',
      'AZ' => 'Azerbaijan',
      'BD' => 'Bangladesh',
      'BH' => 'Bahrain',
      'BN' => 'Brunei',
      'BT' => 'Bhutan',
      'CN' => 'China',
      'CY' => 'Cyprus',
      'GE' => 'Georgia',
      'HK' => 'Hong Kong',
      'ID' => 'Indonesia',
      'IL' => 'Israel',
      'IN' => 'India',
      'IQ' => 'Iraq',
      'IR' => 'Iran',
      'JO' => 'Jordan',
      'JP' => 'Japan',
      'KG' => 'Kyrgyzstan',
      'KH' => 'Cambodia',
      'KP' => 'North Korea',
      'KR' => 'South Korea',
      'KW' => 'Kuwait',
      'KZ' => 'Kazakhstan',
      'LA' => 'Laos',
      'LB' => 'Lebanon',
      'LK' => 'Sri Lanka',
      'MM' => 'Myanmar',
      'MN' => 'Mongolia',
      'MO' => 'Macau',
      'MV' => 'Maldives',
      'MY' => 'Malaysia',
      'NP' => 'Nepal',
      'OM' => 'Oman',
      'PH' => 'Philippines',
      'PK' => 'Pakistan',
      'PS' => 'Palestine',
      'QA' => 'Qatar',
      'SA' => 'Saudi Arabia',
      'SG' => 'Singapore',
      'SY' => 'Syria',
      'TH' => 'Thailand',
      'TJ' => 'Tajikistan',
      'TL' => 'Timor-Leste',
      'TM' => 'Turkmenistan',
      'TR' => 'Turkey',
      'TW' => 'Taiwan',
      'UZ' => 'Uzbekistan',
      'VN' => 'Vietnam',
      'YE' => 'Yemen',
      
      # Europe
      'AD' => 'Andorra',
      'AL' => 'Albania',
      'AT' => 'Austria',
      'AX' => 'Åland Islands',
      'BA' => 'Bosnia and Herzegovina',
      'BE' => 'Belgium',
      'BG' => 'Bulgaria',
      'BY' => 'Belarus',
      'CH' => 'Switzerland',
      'CZ' => 'Czech Republic',
      'DE' => 'Germany',
      'DK' => 'Denmark',
      'EE' => 'Estonia',
      'ES' => 'Spain',
      'FI' => 'Finland',
      'FO' => 'Faroe Islands',
      'FR' => 'France',
      'GB' => 'United Kingdom',
      'GG' => 'Guernsey',
      'GI' => 'Gibraltar',
      'GR' => 'Greece',
      'HR' => 'Croatia',
      'HU' => 'Hungary',
      'IE' => 'Ireland',
      'IM' => 'Isle of Man',
      'IS' => 'Iceland',
      'IT' => 'Italy',
      'JE' => 'Jersey',
      'LI' => 'Liechtenstein',
      'LT' => 'Lithuania',
      'LU' => 'Luxembourg',
      'LV' => 'Latvia',
      'MC' => 'Monaco',
      'MD' => 'Moldova',
      'ME' => 'Montenegro',
      'MK' => 'North Macedonia',
      'MT' => 'Malta',
      'NL' => 'Netherlands',
      'NO' => 'Norway',
      'PL' => 'Poland',
      'PT' => 'Portugal',
      'RO' => 'Romania',
      'RS' => 'Serbia',
      'RU' => 'Russia',
      'SE' => 'Sweden',
      'SI' => 'Slovenia',
      'SJ' => 'Svalbard and Jan Mayen',
      'SK' => 'Slovakia',
      'SM' => 'San Marino',
      'UA' => 'Ukraine',
      'VA' => 'Vatican City',
      'XK' => 'Kosovo',
      
      # Oceania
      'AS' => 'American Samoa',
      'AU' => 'Australia',
      'CK' => 'Cook Islands',
      'FJ' => 'Fiji',
      'FM' => 'Micronesia',
      'GU' => 'Guam',
      'KI' => 'Kiribati',
      'MH' => 'Marshall Islands',
      'MP' => 'Northern Mariana Islands',
      'NC' => 'New Caledonia',
      'NF' => 'Norfolk Island',
      'NR' => 'Nauru',
      'NU' => 'Niue',
      'NZ' => 'New Zealand',
      'PF' => 'French Polynesia',
      'PG' => 'Papua New Guinea',
      'PW' => 'Palau',
      'SB' => 'Solomon Islands',
      'TK' => 'Tokelau',
      'TO' => 'Tonga',
      'TV' => 'Tuvalu',
      'VU' => 'Vanuatu',
      'WF' => 'Wallis and Futuna',
      'WS' => 'Samoa',
      
      # Antarctica
      'AQ' => 'Antarctica',
      'BV' => 'Bouvet Island',
      'GS' => 'South Georgia and South Sandwich Islands',
      'HM' => 'Heard Island and McDonald Islands',
      'TF' => 'French Southern Territories'
    }
    
    countries[country_code.upcase] || country_code.upcase
  end
end
