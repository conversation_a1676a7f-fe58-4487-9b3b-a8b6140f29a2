require 'redcarpet'

module <PERSON><PERSON><PERSON><PERSON>
  def render_markdown(text)
    return '' unless text.present?
    
    # Configure Redcarpet with custom HTML renderer
    renderer = Redcarpet::Render::HTML.new(
      filter_html: false,
      hard_wrap: true,
      link_attributes: { class: 'text-purple-600 hover:text-purple-700 underline', target: '_blank', rel: 'noopener' }
    )
    
    markdown = Redcarpet::Markdown.new(renderer,
      autolink: true,
      tables: true,
      fenced_code_blocks: true,
      disable_indented_code_blocks: true,
      strikethrough: true,
      lax_spacing: true,
      space_after_headers: true,
      superscript: true,
      underline: true,
      highlight: true,
      quote: true,
      footnotes: true
    )
    
    # Render markdown to HTML
    html = markdown.render(text)
    
    # Add Tailwind classes to elements
    html.gsub!('<h1>', '<h1 class="text-3xl font-bold text-gray-900 mt-8 mb-4">')
    html.gsub!('<h2>', '<h2 class="text-2xl font-bold text-gray-900 mt-8 mb-4">')
    html.gsub!('<h3>', '<h3 class="text-xl font-bold text-gray-900 mt-6 mb-3">')
    html.gsub!('<h4>', '<h4 class="text-lg font-bold text-gray-900 mt-4 mb-2">')
    
    html.gsub!('<p>', '<p class="text-gray-600 mb-4 leading-relaxed">')
    html.gsub!('<ul>', '<ul class="list-disc list-inside space-y-2 mb-4 text-gray-600">')
    html.gsub!('<ol>', '<ol class="list-decimal list-inside space-y-2 mb-4 text-gray-600">')
    html.gsub!('<li>', '<li class="ml-4">')
    
    html.gsub!('<blockquote>', '<blockquote class="border-l-4 border-purple-500 pl-4 my-4 italic text-gray-700">')
    html.gsub!('<code>', '<code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">')
    html.gsub!('<pre>', '<pre class="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto my-4">')
    
    html.gsub!('<table>', '<table class="min-w-full divide-y divide-gray-200 my-4">')
    html.gsub!('<thead>', '<thead class="bg-gray-50">')
    html.gsub!('<tbody>', '<tbody class="bg-white divide-y divide-gray-200">')
    html.gsub!('<th>', '<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">')
    html.gsub!('<td>', '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">')
    
    html.gsub!('<strong>', '<strong class="font-bold text-gray-900">')
    html.gsub!('<em>', '<em class="italic">')
    
    html.html_safe
  end
end
