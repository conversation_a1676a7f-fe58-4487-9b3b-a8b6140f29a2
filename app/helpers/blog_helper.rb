module B<PERSON><PERSON><PERSON><PERSON>
  def media_type_badge_classes(media_type)
    case media_type
    when 'video'
      'bg-red-100 text-red-800'
    when 'podcast'
      'bg-green-100 text-green-800'
    when 'pdf'
      'bg-orange-100 text-orange-800'
    when 'infographic', 'image'
      'bg-blue-100 text-blue-800'
    when 'interactive'
      'bg-indigo-100 text-indigo-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def media_type_icon(media_type)
    icon_class = 'w-3 h-3 mr-1'
    
    case media_type
    when 'video'
      content_tag(:svg, class: icon_class, fill: 'currentColor', viewBox: '0 0 24 24') do
        content_tag(:path, nil, d: 'M8 5v14l11-7z')
      end
    when 'podcast'
      content_tag(:svg, class: icon_class, fill: 'currentColor', viewBox: '0 0 24 24') do
        content_tag(:path, nil, d: 'M12 3a9 9 0 000 18 9 9 0 000-18zm-2 8l6-3-6-3v6z')
      end
    when 'pdf'
      content_tag(:svg, class: icon_class, fill: 'currentColor', viewBox: '0 0 24 24') do
        content_tag(:path, nil, d: 'M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z')
      end
    when 'infographic', 'image'
      content_tag(:svg, class: icon_class, fill: 'currentColor', viewBox: '0 0 24 24') do
        content_tag(:path, nil, d: 'M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z')
      end
    when 'interactive'
      content_tag(:svg, class: icon_class, fill: 'currentColor', viewBox: '0 0 24 24') do
        content_tag(:path, nil, d: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z')
      end
    else
      content_tag(:svg, class: icon_class, fill: 'currentColor', viewBox: '0 0 24 24') do
        content_tag(:path, nil, d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z')
      end
    end
  end

  def media_type_label(media_type)
    case media_type
    when 'video'
      'Video'
    when 'podcast'
      'Podcast'
    when 'pdf'
      'PDF Report'
    when 'infographic'
      'Infographic'
    when 'image'
      'Image'
    when 'interactive'
      'Interactive'
    else
      'Article'
    end
  end

end