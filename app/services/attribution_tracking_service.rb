class AttributionTrackingService
  attr_reader :link, :request
  
  def initialize(link:, request:)
    @link = link
    @request = request
  end
  
  def track_click(utm_params = {})
    click = link.link_clicks.build(
      clicked_at: Time.current,
      attribution_data: build_attribution_data(utm_params),
      tracking_data: build_tracking_data
    )
    
    if click.save
      Result.new(success: true, click: click)
    else
      Result.new(success: false, errors: click.errors.to_hash)
    end
  end
  
  def original_url_with_attribution(utm_params)
    uri = URI.parse(link.original_url)
    existing_params = Rack::Utils.parse_query(uri.query || '')
    
    # Merge UTM params, with new params taking precedence
    merged_params = existing_params.merge(utm_params.stringify_keys)
    
    uri.query = merged_params.to_query
    uri.to_s
  end
  
  private
  
  def build_attribution_data(utm_params)
    # Handle both string and symbol keys
    utm_params = utm_params.with_indifferent_access if utm_params.respond_to?(:with_indifferent_access)
    
    {
      utm_source: utm_params[:utm_source] || utm_params['utm_source'],
      utm_medium: utm_params[:utm_medium] || utm_params['utm_medium'],
      utm_campaign: utm_params[:utm_campaign] || utm_params['utm_campaign'],
      utm_term: utm_params[:utm_term] || utm_params['utm_term'],
      utm_content: utm_params[:utm_content] || utm_params['utm_content'],
      referrer: request.referrer
    }.compact
  end
  
  def build_tracking_data
    data = {}
    
    # If Do Not Track is enabled, don't collect any tracking data
    return data if respect_dnt?
    
    # Basic tracking info
    data[:ip_address] = anonymize_ip(request.remote_ip)
    data[:user_agent] = request.user_agent
    
    # Parse user agent for device info
    if request.user_agent
      user_agent_data = parse_user_agent(request.user_agent)
      data.merge!(user_agent_data)
    end
    
    # Geolocation (if IP is available)
    if data[:ip_address]
      begin
        geo_data = GeolocationService.lookup(request.remote_ip)
        data.merge!(geo_data) if geo_data
      rescue StandardError
        # Silently fail geolocation
      end
    end
    
    data
  end
  
  def parse_user_agent(user_agent_string)
    data = {}
    
    # Device type detection
    data[:device_type] = detect_device_type(user_agent_string)
    
    # Browser detection
    data[:browser] = detect_browser(user_agent_string)
    
    # OS detection
    data[:os] = detect_os(user_agent_string)
    
    # Bot detection
    data[:bot] = bot?(user_agent_string)
    
    data
  end
  
  def detect_device_type(user_agent)
    case user_agent
    when /iPad/i
      'tablet'
    when /iPhone|Android.*Mobile|Windows Phone/i
      'mobile'
    when /Android/i
      'tablet' # Android without Mobile is usually tablet
    else
      'desktop'
    end
  end
  
  def detect_browser(user_agent)
    case user_agent
    when /Chrome/i
      'Chrome'
    when /Safari/i
      'Safari'
    when /Firefox/i
      'Firefox'
    when /Edge/i
      'Edge'
    when /Opera|OPR/i
      'Opera'
    else
      'Other'
    end
  end
  
  def detect_os(user_agent)
    case user_agent
    when /iPhone|iPad/i
      'iOS'
    when /Android/i
      'Android'
    when /Windows NT/i
      'Windows'
    when /Mac OS X/i
      'macOS'
    when /Linux/i
      'Linux'
    else
      'Other'
    end
  end
  
  def bot?(user_agent)
    bot_patterns = [
      /bot/i,
      /spider/i,
      /crawl/i,
      /scraper/i,
      /mediapartners/i,
      /adsbot/i,
      /googlebot/i,
      /bingbot/i,
      /slurp/i,
      /duckduckbot/i,
      /baiduspider/i,
      /yandexbot/i,
      /facebookexternalhit/i,
      /twitterbot/i,
      /linkedinbot/i,
      /whatsapp/i,
      /applebot/i
    ]
    
    bot_patterns.any? { |pattern| user_agent.match?(pattern) }
  end
  
  def anonymize_ip(ip)
    return nil if ip.blank?
    
    if gdpr_mode?
      # Anonymize last octet for IPv4
      if ip.include?('.')
        parts = ip.split('.')
        parts[-1] = '0'
        parts.join('.')
      else
        # For IPv6, anonymize last groups
        ip.split(':')[0..3].join(':') + '::'
      end
    else
      ip
    end
  end
  
  def respect_dnt?
    request.headers['DNT'] == '1'
  end
  
  def gdpr_mode?
    Rails.application.config.respond_to?(:gdpr_mode) && Rails.application.config.gdpr_mode
  end
  
  class Result
    attr_reader :click, :errors
    
    def initialize(success:, click: nil, errors: {})
      @success = success
      @click = click
      @errors = errors
    end
    
    def success?
      @success
    end
  end
end