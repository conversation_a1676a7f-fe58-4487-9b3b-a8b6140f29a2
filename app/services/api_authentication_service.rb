class ApiAuthenticationService
  def authenticate(token)
    return Result.new(success: false, error: :missing_token, message: 'Authorization header is missing') if token.blank?

    api_token = ApiToken.active.find_by(token: token)
    return Result.new(success: false, error: :invalid_token, message: 'Invalid or expired API token') unless api_token

    api_token.refresh!
    Result.new(success: true, user: api_token.user, api_token: api_token)
  rescue StandardError => e
    Rails.logger.error("API authentication error: #{e.message}")
    Result.new(success: false, error: :authentication_error, message: 'Authentication failed')
  end

  def extract_token_from_header(header)
    return nil if header.blank?
    
    match = header.match(/\Abearer\s+(.+)\z/i)
    match&.captures&.first
  end

  class Result
    attr_reader :user, :api_token, :error, :message

    def initialize(success:, user: nil, api_token: nil, error: nil, message: nil)
      @success = success
      @user = user
      @api_token = api_token
      @error = error
      @message = message
    end

    def success?
      @success
    end

    def failure?
      !@success
    end
  end
end