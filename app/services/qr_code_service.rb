class QrCodeService
  attr_reader :link, :options

  def initialize(link, options = {})
    @link = link
    @options = default_options.merge(options)
  end

  def generate_svg
    qr_code.as_svg(
      offset: 0,
      color: options[:color],
      shape_rendering: 'crispEdges',
      module_size: options[:size],
      svg_attributes: {
        class: 'qr-code-svg w-full h-full',
        style: 'max-width: 300px; height: auto;'
      }
    )
  end

  def generate_png
    qr_code.as_png(
      bit_depth: 1,
      border_modules: 4,
      color_mode: ChunkyPNG::COLOR_GRAYSCALE,
      color: options[:color],
      file: nil,
      fill: 'white',
      module_px_size: options[:size] * 10,
      resize_exactly_to: options[:png_size],
      resize_gte_to: false,
      size: options[:png_size]
    )
  end

  def to_data_url
    "data:image/png;base64,#{Base64.strict_encode64(generate_png.to_s)}"
  end

  private

  def qr_code
    @qr_code ||= ::RQRCode::QRCode.new(
      link.short_url,
      level: options[:error_correction],
      mode: options[:mode]
    )
  end

  def default_options
    {
      size: 6,                     # Module size for SVG
      png_size: 300,              # PNG size in pixels
      color: '000',               # QR code color
      error_correction: :m,       # Error correction level (l, m, q, h)
      mode: nil,                  # Let rqrcode decide the best mode
      format: :svg                # Default format
    }
  end
end