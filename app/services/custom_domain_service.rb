class CustomDomainService
  attr_reader :user, :domain

  def initialize(user, domain = nil)
    @user = user
    @domain = domain
  end

  def add_domain(domain_name)
    # Clean and validate the domain
    domain_name = clean_domain(domain_name)
    
    # Check if domain already exists
    if CustomDomain.exists?(domain: domain_name)
      return ServiceResult.new(
        success: false,
        errors: { domain: ['has already been taken'] }
      )
    end

    # Check user's domain limit based on subscription
    if user_at_domain_limit?
      return ServiceResult.new(
        success: false,
        errors: { base: ['You have reached your custom domain limit for your subscription plan'] }
      )
    end

    # Create the custom domain
    custom_domain = user.custom_domains.build(
      domain: domain_name,
      verification_token: generate_verification_token,
      verified: false
    )

    if custom_domain.save
      ServiceResult.new(
        success: true,
        custom_domain: custom_domain,
        verification_instructions: verification_instructions(custom_domain)
      )
    else
      ServiceResult.new(
        success: false,
        errors: custom_domain.errors
      )
    end
  end

  def verify_domain(custom_domain)
    # In production, this would check DNS records
    # For now, we'll simulate verification
    dns_records = check_dns_records(custom_domain)
    
    if dns_records[:txt_valid] && dns_records[:cname_valid]
      custom_domain.verify!
      ServiceResult.new(
        success: true,
        custom_domain: custom_domain,
        message: 'Domain verified successfully!'
      )
    else
      ServiceResult.new(
        success: false,
        errors: { base: ['DNS records not found or invalid'] },
        dns_status: dns_records
      )
    end
  end

  def remove_domain(custom_domain)
    if custom_domain.destroy
      ServiceResult.new(success: true, message: 'Domain removed successfully')
    else
      ServiceResult.new(
        success: false,
        errors: custom_domain.errors
      )
    end
  end

  def set_primary_domain(custom_domain)
    unless custom_domain.verified?
      return ServiceResult.new(
        success: false,
        errors: { base: ['Domain must be verified before setting as primary'] }
      )
    end

    ActiveRecord::Base.transaction do
      # Remove primary status from other domains
      user.custom_domains.where.not(id: custom_domain.id).update_all(is_primary: false)
      
      # Set this domain as primary
      custom_domain.update!(is_primary: true)
    end

    ServiceResult.new(
      success: true,
      custom_domain: custom_domain,
      message: 'Primary domain updated successfully'
    )
  rescue ActiveRecord::RecordInvalid => e
    ServiceResult.new(
      success: false,
      errors: { base: [e.message] }
    )
  end

  private

  def clean_domain(domain_name)
    domain_name.to_s.downcase.strip.gsub(/^https?:\/\//, '').gsub(/\/$/, '')
  end

  def generate_verification_token
    "linkmaster-verify-#{SecureRandom.hex(16)}"
  end

  def user_at_domain_limit?
    case user.subscription_plan
    when 'free'
      user.custom_domains.count >= 0 # No custom domains for free plan
    when 'professional'
      user.custom_domains.count >= 3
    when 'business'
      user.custom_domains.count >= 10
    when 'enterprise'
      false # Unlimited for enterprise
    else
      user.custom_domains.count >= 1 # Default to 1
    end
  end

  def verification_instructions(custom_domain)
    {
      txt_record: {
        type: 'TXT',
        name: '_linkmaster-verify',
        value: custom_domain.verification_token,
        ttl: '3600'
      },
      cname_record: {
        type: 'CNAME',
        name: '@',
        value: 'links.linkmaster.app',
        ttl: '3600'
      },
      instructions: [
        'Add the TXT record to verify ownership',
        'Add the CNAME record to point your domain to LinkMaster',
        'Verification usually takes 5-30 minutes',
        'Click "Verify Domain" once DNS records are added'
      ]
    }
  end

  def check_dns_records(custom_domain)
    # In production, this would use a DNS resolver to check actual records
    # For development, we'll simulate the check
    {
      txt_valid: simulate_dns_check,
      cname_valid: simulate_dns_check,
      txt_value: custom_domain.verification_token,
      cname_value: 'links.linkmaster.app'
    }
  end

  def simulate_dns_check
    # In development, randomly succeed 70% of the time
    Rails.env.development? ? rand > 0.3 : false
  end

  # Service result object
  class ServiceResult
    attr_reader :success, :errors, :custom_domain, :message, :verification_instructions, :dns_status

    def initialize(success:, errors: {}, custom_domain: nil, message: nil, verification_instructions: nil, dns_status: nil)
      @success = success
      @errors = errors
      @custom_domain = custom_domain
      @message = message
      @verification_instructions = verification_instructions
      @dns_status = dns_status
    end

    def success?
      @success
    end
  end
end