<div class="px-6 py-4 border-b border-gray-200">
  <h2 class="text-lg font-semibold text-gray-900">Analytics & Tracking</h2>
  <p class="text-sm text-gray-600">Configure analytics and tracking integrations</p>
</div>

<div class="p-6">
  <% if local_assigns[:success] %>
    <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
      Integration settings updated successfully!
    </div>
  <% end %>

  <%= form_with model: user, url: settings_integration_settings_path, method: :patch, local: true, data: { turbo_frame: "_top" } do |form| %>
    <div class="space-y-8">
      <!-- Google Analytics -->
      <div class="border border-gray-200 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-base font-medium text-gray-900">Google Analytics</h3>
              <p class="text-sm text-gray-500">Track link clicks in Google Analytics</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <%= form.check_box :google_analytics_enabled, 
                              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                              data: { action: "change->integrations#toggleSection", section: "google-analytics" } %>
          </div>
        </div>

        <div id="google-analytics-config" class="<%= 'hidden' unless user.google_analytics_enabled %>">
          <%= form.label :google_analytics_tracking_id, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :google_analytics_tracking_id, 
                             class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                             placeholder: "G-XXXXXXXXXX or UA-XXXXXXXX-X" %>
        </div>
      </div>

      <!-- Facebook Pixel -->
      <div class="border border-gray-200 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-base font-medium text-gray-900">Facebook Pixel</h3>
              <p class="text-sm text-gray-500">Track conversions with Facebook Pixel</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <%= form.check_box :facebook_pixel_enabled, 
                              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                              data: { action: "change->integrations#toggleSection", section: "facebook-pixel" } %>
          </div>
        </div>

        <div id="facebook-pixel-config" class="<%= 'hidden' unless user.facebook_pixel_enabled %>">
          <%= form.label :facebook_pixel_id, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :facebook_pixel_id, 
                             class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                             placeholder: "Enter your Facebook Pixel ID" %>
        </div>
      </div>

      <!-- Slack -->
      <div class="border border-gray-200 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-base font-medium text-gray-900">Slack</h3>
              <p class="text-sm text-gray-500">Get notifications in Slack</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <%= form.check_box :slack_enabled, 
                              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                              data: { action: "change->integrations#toggleSection", section: "slack" } %>
          </div>
        </div>

        <div id="slack-config" class="<%= 'hidden' unless user.slack_enabled %>">
          <%= form.label :slack_webhook_url, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :slack_webhook_url, 
                             class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                             placeholder: "https://hooks.slack.com/services/..." %>
        </div>
      </div>

      <!-- Zapier -->
      <div class="border border-gray-200 rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm-.5 5.5h1V10l4.5-4.5h1L12.5 11H17v1h-4.5l5.5 5.5h-1L12.5 13V17.5h-1V13L7 17.5H6l4.5-4.5H6v-1h4.5L6 7.5h1L11.5 11V5.5z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-base font-medium text-gray-900">Zapier</h3>
              <p class="text-sm text-gray-500">Connect with thousands of apps</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <%= form.check_box :zapier_enabled, 
                              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                              data: { action: "change->integrations#toggleSection", section: "zapier" } %>
          </div>
        </div>

        <div id="zapier-config" class="<%= 'hidden' unless user.zapier_enabled %>">
          <%= form.label :zapier_webhook_url, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :zapier_webhook_url, 
                             class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                             placeholder: "https://hooks.zapier.com/hooks/catch/..." %>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex justify-end space-x-3">
      <%= form.submit "Save Integration Settings", 
                     class: "px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",
                     data: { disable_with: "Saving..." } %>
    </div>
  <% end %>
</div>