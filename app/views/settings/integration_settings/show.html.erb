<% content_for :title, "Integration Settings - LinkMaster" %>

<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
        <%= link_to "Settings", settings_path, class: "hover:text-gray-700" %>
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-gray-900">Integrations</span>
      </nav>
      <h1 class="text-3xl font-bold text-gray-900">Integration Settings</h1>
      <p class="mt-2 text-gray-600">Connect LinkMaster with your favorite tools and services</p>
    </div>

    <div class="bg-white rounded-lg shadow">
      <div id="integration-settings">
        <%= render 'integration_settings/form', user: @user %>
      </div>
    </div>

    <!-- Available Integrations -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @available_integrations.each do |integration| %>
        <div class="bg-white rounded-lg shadow p-6 border <%= integration[:enabled] ? 'border-green-200' : 'border-gray-200' %>">
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900"><%= integration[:name] %></h3>
              <p class="text-sm text-gray-600 mt-1"><%= integration[:description] %></p>
            </div>
            <div class="flex-shrink-0 ml-4">
              <% if integration[:enabled] %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Connected
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  Not Connected
                </span>
              <% end %>
            </div>
          </div>

          <% if integration[:enabled] %>
            <!-- Integration Configuration -->
            <div class="space-y-3">
              <div class="bg-green-50 border border-green-200 rounded-md p-3">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-green-700">
                      <%= integration[:name] %> is connected and working properly.
                    </p>
                  </div>
                </div>
              </div>

              <div class="flex space-x-2">
                <button type="button" 
                        class="flex-1 px-3 py-2 bg-gray-600 text-white text-sm font-medium rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                        data-action="click->integrations#configure"
                        data-integration="<%= integration[:id] %>">
                  Configure
                </button>
                <button type="button" 
                        class="px-3 py-2 bg-red-600 text-white text-sm font-medium rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        data-action="click->integrations#disconnect"
                        data-integration="<%= integration[:id] %>">
                  Disconnect
                </button>
              </div>
            </div>
          <% else %>
            <!-- Connect Integration -->
            <button type="button" 
                    class="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    data-action="click->integrations#connect"
                    data-integration="<%= integration[:id] %>">
              Connect <%= integration[:name] %>
            </button>
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Custom Webhooks -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Custom Webhooks</h2>
        <p class="text-sm text-gray-600">Configure custom webhooks for advanced integrations</p>
      </div>
      
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Webhook URL</label>
            <input type="url" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                   placeholder="https://your-app.com/webhook/linkmaster">
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Events to Send</label>
            <div class="space-y-2">
              <div class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                <label class="ml-2 text-sm text-gray-700">Link Created</label>
              </div>
              <div class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                <label class="ml-2 text-sm text-gray-700">Link Clicked</label>
              </div>
              <div class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label class="ml-2 text-sm text-gray-700">Link Updated</label>
              </div>
              <div class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label class="ml-2 text-sm text-gray-700">Link Deleted</label>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button type="button" class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
              Test Webhook
            </button>
            <button type="button" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Save Webhook
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>