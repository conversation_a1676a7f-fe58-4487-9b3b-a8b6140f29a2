<div id="api-token-<%= api_token.id %>" class="border border-gray-200 rounded-lg p-4">
  <div class="flex items-start justify-between">
    <div class="flex-1">
      <div class="flex items-center space-x-3 mb-2">
        <h3 class="text-sm font-medium text-gray-900"><%= api_token.name %></h3>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          Active
        </span>
      </div>
      
      <% if api_token.description.present? %>
        <p class="text-sm text-gray-600 mb-3"><%= api_token.description %></p>
      <% end %>

      <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-xs text-gray-500">
        <div>
          <span class="font-medium">Created:</span><br>
          <%= api_token.created_at.strftime("%b %d, %Y") %>
        </div>
        <div>
          <span class="font-medium">Last used:</span><br>
          <%= api_token.last_used_at&.strftime("%b %d, %Y") || "Never" %>
        </div>
        <div>
          <span class="font-medium">Token ID:</span><br>
          <code class="bg-gray-100 px-1 rounded"><%= api_token.token.first(8) %>...</code>
        </div>
      </div>

      <!-- Token Display (only shown immediately after creation) -->
      <% if local_assigns[:show_token] %>
        <div class="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div class="flex items-center justify-between">
            <div class="flex-1 mr-3">
              <label class="block text-xs font-medium text-gray-700 mb-1">Your API Token</label>
              <code class="block p-2 bg-white border border-gray-300 rounded text-sm font-mono break-all" 
                    data-controller="clipboard" 
                    data-clipboard-target="source">
                <%= api_token.token %>
              </code>
            </div>
            <button type="button" 
                    class="flex-shrink-0 px-3 py-2 bg-gray-600 text-white text-xs font-medium rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    data-action="click->clipboard#copy">
              Copy
            </button>
          </div>
          <p class="mt-2 text-xs text-red-600">
            ⚠️ This token will not be shown again. Copy it now and store it securely.
          </p>
        </div>
      <% end %>
    </div>

    <div class="flex-shrink-0 ml-4">
      <%= button_to settings_api_token_path(api_token), 
                    method: :delete,
                    class: "text-red-600 hover:text-red-700 text-sm font-medium",
                    data: { 
                      confirm: "Are you sure? This action cannot be undone.",
                      turbo_method: :delete
                    } do %>
        Revoke
      <% end %>
    </div>
  </div>
</div>