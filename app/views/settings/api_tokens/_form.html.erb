<%= form_with model: api_token, url: settings_api_tokens_path, local: true, data: { turbo_frame: "_top" } do |form| %>
  <div class="space-y-4">
    <div>
      <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
      <%= form.text_field :name, 
                         class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                         placeholder: "Token name (e.g., Production API)" %>
      <% if api_token.errors[:name].any? %>
        <p class="mt-1 text-sm text-red-600"><%= api_token.errors[:name].first %></p>
      <% end %>
    </div>

    <div>
      <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" %>
      <%= form.text_area :description, 
                        rows: 3,
                        class: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",
                        placeholder: "Brief description of how this token will be used" %>
    </div>

    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">
            Security Notice
          </h3>
          <div class="mt-2 text-sm text-blue-700">
            <p>Your token will only be displayed once after creation. Make sure to copy and store it securely.</p>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-end">
      <%= form.submit "Create Token", 
                     class: "px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",
                     data: { disable_with: "Creating..." } %>
    </div>
  </div>
<% end %>