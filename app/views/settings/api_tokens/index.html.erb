<% content_for :title, "API Tokens - LinkMaster" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
        <%= link_to settings_path, class: "hover:text-gray-700 transition-colors" do %>
          <span>Settings</span>
        <% end %>
        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-gray-900 font-medium">API Tokens</span>
      </nav>
      
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
            API Tokens
          </h1>
          <p class="text-gray-600">Manage your API tokens for programmatic access to LinkMaster</p>
        </div>
        <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2a2 2 0 00-2-2m0 0a2 2 0 012-2m0 0a2 2 0 012 2m-6 4h4"></path>
          </svg>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Create New Token -->
      <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
        <div class="px-6 py-5 bg-gradient-to-br from-purple-50 to-indigo-50 border-b border-purple-100">
          <h2 class="text-lg font-bold text-gray-900 flex items-center gap-2">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center shadow-md">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
            Create New Token
          </h2>
        </div>
        
        <div class="p-6" id="api-token-form">
          <%= render 'form', api_token: @api_token %>
        </div>
      </div>

      <!-- Active Tokens -->
      <div class="lg:col-span-2">
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-indigo-500/5 transition-all duration-300">
          <div class="px-8 py-6 bg-gradient-to-br from-indigo-50 to-purple-50 border-b border-indigo-100">
            <h2 class="text-xl font-bold text-gray-900 mb-1">Active Tokens</h2>
            <p class="text-sm text-gray-600">Your active API tokens and their permissions</p>
          </div>

          <div class="p-6">
            <% if @api_tokens.any? %>
              <div class="space-y-4" id="api-tokens-list">
                <% @api_tokens.each do |token| %>
                  <div class="group bg-gray-50 rounded-xl border border-gray-200 p-5 hover:shadow-md hover:shadow-indigo-500/5 transition-all duration-300">
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                          <h4 class="font-semibold text-gray-900"><%= token.name %></h4>
                          <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            Active
                          </span>
                        </div>
                        
                        <div class="space-y-2 text-sm text-gray-600">
                          <div class="flex items-center gap-2">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Created <%= token.created_at.strftime('%B %d, %Y') %></span>
                          </div>
                          
                          <% if token.last_used_at.present? %>
                            <div class="flex items-center gap-2">
                              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                              </svg>
                              <span>Last used <%= time_ago_in_words(token.last_used_at) %> ago</span>
                            </div>
                          <% else %>
                            <div class="flex items-center gap-2">
                              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                              </svg>
                              <span class="text-gray-500">Never used</span>
                            </div>
                          <% end %>
                          
                          <div class="flex items-center gap-2">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            <span>Permissions: <%= token.scopes.join(', ') %></span>
                          </div>
                        </div>
                      </div>
                      
                      <div class="ml-4">
                        <%= link_to settings_api_token_path(token), method: :delete, 
                                    data: { confirm: "Are you sure you want to revoke this token?" },
                                    class: "text-sm font-medium text-red-600 hover:text-red-700 transition-colors" do %>
                          Revoke
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-16">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4">
                  <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2m-2-2a2 2 0 00-2 2m2-2a2 2 0 00-2-2m0 0a2 2 0 012-2m0 0a2 2 0 012 2m-6 4h4"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No API tokens</h3>
                <p class="text-gray-600 max-w-sm mx-auto">Get started by creating your first API token for programmatic access.</p>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- API Documentation -->
    <div class="mt-8 group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
      <div class="px-8 py-6 bg-gradient-to-br from-blue-50 to-cyan-50 border-b border-blue-100">
        <h2 class="text-xl font-bold text-gray-900 flex items-center gap-2">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
          </div>
          API Documentation
        </h2>
      </div>
      
      <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 p-6 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl mb-4 shadow-md">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">Getting Started</h3>
            <p class="text-sm text-gray-600 mb-4">Learn how to authenticate and make your first API call</p>
            <%= link_to api_docs_path, class: "inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors" do %>
              View Guide
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            <% end %>
          </div>

          <div class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 p-6 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl mb-4 shadow-md">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">API Reference</h3>
            <p class="text-sm text-gray-600 mb-4">Complete reference for all available endpoints</p>
            <%= link_to api_docs_path(anchor: 'reference'), class: "inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors" do %>
              View Reference
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            <% end %>
          </div>

          <div class="group bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200 p-6 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl mb-4 shadow-md">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
              </svg>
            </div>
            <h3 class="font-bold text-gray-900 mb-2">Code Examples</h3>
            <p class="text-sm text-gray-600 mb-4">Sample code in popular programming languages</p>
            <%= link_to api_docs_path(anchor: 'examples'), class: "inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors" do %>
              View Examples
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Security Notice -->
    <div class="mt-6 p-5 bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl border border-amber-200">
      <div class="flex items-start gap-3">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-md">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <h3 class="font-semibold text-amber-900 mb-1">Security Best Practices</h3>
          <p class="text-sm text-amber-800">
            Keep your API tokens secure. Never share them publicly or commit them to version control. 
            Rotate tokens regularly and revoke any that may have been compromised.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>