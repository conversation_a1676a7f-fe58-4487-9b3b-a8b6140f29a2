<%= turbo_stream.prepend "custom_domains" do %>
  <div class="bg-white rounded-lg border border-gray-200 p-6" id="<%= dom_id(@custom_domain) %>">
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div class="flex items-center gap-3">
          <h3 class="text-lg font-medium text-gray-900"><%= @custom_domain.domain %></h3>
          <span class="px-2 py-1 text-xs font-medium <%= @custom_domain.status_badge_class %> rounded-full">
            <%= @custom_domain.status.capitalize %>
          </span>
        </div>
        
        <div class="mt-4 bg-gray-50 rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-2">DNS Configuration Required</h4>
          <p class="text-sm text-gray-600 mb-3">Add these DNS records to verify your domain:</p>
          
          <div class="space-y-3">
            <div class="bg-white rounded border border-gray-200 p-3">
              <div class="grid grid-cols-4 gap-2 text-xs">
                <div>
                  <span class="font-medium text-gray-700">Type:</span>
                  <p class="font-mono">TXT</p>
                </div>
                <div>
                  <span class="font-medium text-gray-700">Name:</span>
                  <p class="font-mono">_linkmaster-verify</p>
                </div>
                <div class="col-span-2">
                  <span class="font-medium text-gray-700">Value:</span>
                  <p class="font-mono break-all"><%= @custom_domain.verification_token %></p>
                </div>
              </div>
            </div>
            
            <div class="bg-white rounded border border-gray-200 p-3">
              <div class="grid grid-cols-4 gap-2 text-xs">
                <div>
                  <span class="font-medium text-gray-700">Type:</span>
                  <p class="font-mono">CNAME</p>
                </div>
                <div>
                  <span class="font-medium text-gray-700">Name:</span>
                  <p class="font-mono">@</p>
                </div>
                <div class="col-span-2">
                  <span class="font-medium text-gray-700">Value:</span>
                  <p class="font-mono">links.linkmaster.app</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="ml-4 flex items-center gap-2">
        <%= button_to "Verify Domain", 
            settings_custom_domain_path(@custom_domain), 
            method: :patch,
            params: { action_type: 'verify' },
            class: "px-4 py-2 text-sm font-medium text-purple-600 hover:text-purple-700",
            data: { turbo_method: :patch } %>
        
        <%= button_to "Remove", 
            settings_custom_domain_path(@custom_domain), 
            method: :delete,
            class: "px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700",
            data: { 
              turbo_method: :delete,
              turbo_confirm: "Are you sure you want to remove this domain?" 
            } %>
      </div>
    </div>
  </div>
<% end %>

<%= turbo_stream.update "custom_domain_form" do %>
  <%= form_with model: [:settings, CustomDomain.new], local: false do |f| %>
    <div class="flex gap-4">
      <div class="flex-1">
        <%= f.text_field :domain, 
            placeholder: "yourdomain.com", 
            class: "w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",
            required: true %>
      </div>
      <%= f.submit "Add Domain", 
          class: "px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors cursor-pointer" %>
    </div>
    <p class="mt-2 text-sm text-gray-500">
      Enter your domain without http:// or https://
    </p>
  <% end %>
<% end %>

<%= turbo_stream.update "flash", partial: "shared/flash", locals: { flash: { notice: "Custom domain added successfully. Please verify it." } } %>