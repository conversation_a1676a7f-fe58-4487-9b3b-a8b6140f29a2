<% content_for :title, "Custom Domains - LinkMaster" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
  <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
        <%= link_to settings_path, class: "hover:text-gray-700 transition-colors" do %>
          <span>Settings</span>
        <% end %>
        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-gray-900 font-medium">Custom Domains</span>
      </nav>
      
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
            Custom Domains
          </h1>
          <p class="text-gray-600">
            Use your own domain for branded short links
            <% if current_user.subscription_plan == 'free' %>
              <span class="block mt-1 text-amber-600 font-medium">Upgrade to Professional to use custom domains</span>
            <% end %>
          </p>
        </div>
        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center shadow-lg">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Add Domain Form -->
    <% if @can_add_domain %>
      <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300 mb-6">
        <div class="px-8 py-6 bg-gradient-to-br from-blue-50 to-cyan-50 border-b border-blue-100">
          <h2 class="text-xl font-bold text-gray-900 flex items-center gap-2">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
            Add New Domain
          </h2>
        </div>
        
        <div class="p-8">
          <%= form_with model: [:settings, CustomDomain.new], local: false do |f| %>
            <div class="flex gap-4">
              <div class="flex-1">
                <%= f.text_field :domain, 
                    placeholder: "yourdomain.com", 
                    class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                    required: true %>
              </div>
              <%= f.submit "Add Domain", 
                  class: "px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-cyan-700 transition-all duration-200 shadow-lg shadow-blue-500/25 cursor-pointer" %>
            </div>
            <p class="mt-3 text-sm text-gray-500">
              Enter your domain without http:// or https://. We'll guide you through the verification process.
            </p>
          <% end %>
        </div>
      </div>
    <% elsif current_user.subscription_plan == 'free' %>
      <div class="group bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl shadow-sm border border-amber-200 overflow-hidden hover:shadow-lg hover:shadow-amber-500/5 transition-all duration-300 mb-6">
        <div class="p-8">
          <div class="flex items-start gap-4">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
              </div>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-bold text-amber-900 mb-1">Upgrade Required</h3>
              <p class="text-amber-800 mb-4">
                Custom domains are available on Professional plans and above. Unlock branded links and boost your brand presence.
              </p>
              <%= link_to pricing_path, class: "inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-amber-600 to-orange-600 text-white font-medium rounded-xl hover:from-amber-700 hover:to-orange-700 transition-all duration-200 shadow-lg shadow-amber-500/25" do %>
                Upgrade Now
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% else %>
      <div class="group bg-gradient-to-br from-yellow-50 to-amber-50 rounded-2xl shadow-sm border border-yellow-200 overflow-hidden hover:shadow-lg hover:shadow-yellow-500/5 transition-all duration-300 mb-6">
        <div class="p-6">
          <div class="flex items-center gap-3">
            <svg class="w-5 h-5 text-yellow-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="text-yellow-800">
              You've reached the maximum number of custom domains for your plan.
              <%= link_to "Upgrade", pricing_path, class: "font-medium text-yellow-900 hover:text-yellow-800 underline transition-colors" %> to add more domains.
            </p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Domains List -->
    <div class="space-y-4">
      <% @custom_domains.each do |domain| %>
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-cyan-500/5 transition-all duration-300" id="<%= dom_id(domain) %>">
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-3">
                  <h3 class="text-xl font-bold text-gray-900"><%= domain.domain %></h3>
                  <% if domain.is_primary? %>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-indigo-100 text-purple-800 border border-purple-200">
                      <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      Primary
                    </span>
                  <% end %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= domain.status_badge_class %> border">
                    <%= domain.status.capitalize %>
                  </span>
                </div>
                
                <% if domain.verified? %>
                  <div class="flex items-center gap-2 text-sm text-gray-600">
                    <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span>Verified on <%= domain.verified_at.strftime("%B %d, %Y") %></span>
                  </div>
                <% else %>
                  <div class="mt-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                    <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      DNS Configuration Required
                    </h4>
                    <p class="text-sm text-gray-600 mb-4">Add these DNS records to verify your domain:</p>
                    
                    <div class="space-y-3">
                      <!-- TXT Record -->
                      <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <div class="grid grid-cols-1 sm:grid-cols-4 gap-3 text-sm">
                          <div>
                            <span class="font-semibold text-gray-700 block mb-1">Type</span>
                            <code class="px-2 py-1 bg-blue-50 text-blue-800 rounded text-xs">TXT</code>
                          </div>
                          <div>
                            <span class="font-semibold text-gray-700 block mb-1">Name</span>
                            <code class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">_linkmaster-verify</code>
                          </div>
                          <div class="sm:col-span-2">
                            <span class="font-semibold text-gray-700 block mb-1">Value</span>
                            <code class="block px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs break-all"><%= domain.verification_token %></code>
                          </div>
                        </div>
                      </div>
                      
                      <!-- CNAME Record -->
                      <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <div class="grid grid-cols-1 sm:grid-cols-4 gap-3 text-sm">
                          <div>
                            <span class="font-semibold text-gray-700 block mb-1">Type</span>
                            <code class="px-2 py-1 bg-green-50 text-green-800 rounded text-xs">CNAME</code>
                          </div>
                          <div>
                            <span class="font-semibold text-gray-700 block mb-1">Name</span>
                            <code class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">@</code>
                          </div>
                          <div class="sm:col-span-2">
                            <span class="font-semibold text-gray-700 block mb-1">Value</span>
                            <code class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">links.linkmaster.app</code>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
              
              <div class="ml-6 flex items-center gap-2 flex-shrink-0">
                <% if !domain.verified? %>
                  <%= button_to "Verify Domain", 
                      settings_custom_domain_path(domain), 
                      method: :patch,
                      params: { action_type: 'verify' },
                      class: "px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-lg transition-all duration-200",
                      data: { turbo_method: :patch } %>
                <% elsif !domain.is_primary? %>
                  <%= button_to "Set as Primary", 
                      settings_custom_domain_path(domain), 
                      method: :patch,
                      params: { action_type: 'set_primary' },
                      class: "px-4 py-2 text-sm font-medium text-purple-600 hover:text-purple-700 bg-purple-50 hover:bg-purple-100 rounded-lg transition-all duration-200",
                      data: { turbo_method: :patch } %>
                <% end %>
                
                <%= button_to "Remove", 
                    settings_custom_domain_path(domain), 
                    method: :delete,
                    class: "px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 bg-red-50 hover:bg-red-100 rounded-lg transition-all duration-200",
                    data: { 
                      turbo_method: :delete,
                      turbo_confirm: "Are you sure you want to remove this domain?" 
                    } %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
      
      <% if @custom_domains.empty? %>
        <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-12 text-center">
          <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl mb-4">
            <svg class="w-10 h-10 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-2">No custom domains yet</h3>
          <p class="text-gray-600 max-w-sm mx-auto">
            <% if current_user.subscription_plan == 'free' %>
              Upgrade to Professional to start using custom domains for your branded links.
            <% else %>
              Add your first custom domain to create branded short links that build trust.
            <% end %>
          </p>
        </div>
      <% end %>
    </div>

    <!-- Help Section -->
    <div class="mt-8 p-6 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl border border-blue-200">
      <div class="flex items-start gap-3">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <h3 class="font-semibold text-blue-900 mb-1">Need help with DNS configuration?</h3>
          <p class="text-sm text-blue-800 mb-3">
            Check our guide for step-by-step instructions on configuring DNS with popular domain registrars.
          </p>
          <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
            View DNS Setup Guide →
          </a>
        </div>
      </div>
    </div>
  </div>
</div>