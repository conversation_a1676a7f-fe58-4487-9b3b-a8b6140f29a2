<div id="account-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Account Settings</h2>
      <p class="text-sm text-gray-600">Update your personal information and preferences</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
      </svg>
    </div>
  </div>

  <% if local_assigns[:success] %>
    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-sm font-medium text-green-800">Account settings updated successfully!</p>
      </div>
    </div>
  <% end %>

  <%= form_with model: user, url: update_account_settings_path, method: :patch, local: true, data: { controller: "form-validation", turbo_frame: "_top" } do |form| %>
    <div class="space-y-8">
      <!-- Profile Information Section -->
      <div>
        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          Profile Information
        </h3>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div>
            <%= form.label :first_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :first_name, 
                               class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                               placeholder: "John",
                               data: { action: "blur->form-validation#validateField" } %>
            <% if user.errors[:first_name].any? %>
              <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <%= user.errors[:first_name].first %>
              </p>
            <% end %>
          </div>

          <div>
            <%= form.label :last_name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :last_name, 
                               class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                               placeholder: "Doe",
                               data: { action: "blur->form-validation#validateField" } %>
            <% if user.errors[:last_name].any? %>
              <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <%= user.errors[:last_name].first %>
              </p>
            <% end %>
          </div>

          <div class="sm:col-span-2">
            <%= form.label :email, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <div class="relative">
              <%= form.email_field :email, 
                                  class: "w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                  placeholder: "<EMAIL>",
                                  data: { action: "blur->form-validation#validateField" } %>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
            </div>
            <% if user.errors[:email].any? %>
              <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <%= user.errors[:email].first %>
              </p>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Professional Information Section -->
      <div>
        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          Professional Information
        </h3>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div>
            <%= form.label :company, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :company, 
                               class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                               placeholder: "Acme Corp" %>
          </div>

          <div>
            <%= form.label :job_title, class: "block text-sm font-semibold text-gray-700 mb-2" %>
            <%= form.text_field :job_title, 
                               class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                               placeholder: "Marketing Manager" %>
          </div>
        </div>
      </div>

      <!-- Preferences Section -->
      <div>
        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
            </svg>
          </div>
          Preferences
        </h3>
        
        <div>
          <%= form.label :timezone, class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <div class="relative">
            <%= form.select :timezone, 
                           options_from_collection_for_select(ActiveSupport::TimeZone.all, :name, :to_s, user.timezone || Time.zone.name),
                           { prompt: "Select timezone" },
                           { class: "w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 appearance-none bg-white" } %>
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>
          <p class="mt-2 text-xs text-gray-500">Your timezone affects how dates and times are displayed</p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex items-center justify-end gap-3">
      <%= link_to "Cancel", settings_path, 
                  class: "px-6 py-2.5 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors" %>
      <%= form.submit "Save Changes", 
                     class: "px-6 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg shadow-purple-500/25 cursor-pointer",
                     data: { disable_with: "Saving..." } %>
    </div>
  <% end %>

  <!-- Danger Zone -->
  <div class="mt-12 pt-8 border-t border-gray-200">
    <div class="group bg-white rounded-2xl border border-red-200 p-6 hover:shadow-lg hover:shadow-red-500/5 transition-all duration-300">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-bold text-red-900">Danger Zone</h3>
      </div>
      
      <p class="text-sm text-gray-600 mb-4">
        Once you delete your account, there is no going back. All your data, links, and analytics will be permanently removed.
      </p>
      
      <button type="button" 
              class="px-6 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm font-medium rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 shadow-lg shadow-red-500/25"
              data-action="click->settings#confirmDeleteAccount">
        Delete Account
      </button>
    </div>
  </div>
</div>