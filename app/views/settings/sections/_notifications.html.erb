<div id="notification-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Notification Preferences</h2>
      <p class="text-sm text-gray-600">Manage how and when you receive notifications from LinkMaster</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
      </svg>
    </div>
  </div>

  <% if local_assigns[:success] %>
    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-sm font-medium text-green-800">Notification preferences updated successfully!</p>
      </div>
    </div>
  <% end %>

  <%= form_with model: user, url: update_notifications_settings_path, method: :patch, local: true, data: { turbo_frame: "_top" } do |form| %>
    <div class="space-y-8">
      <!-- Email Notifications Section -->
      <div>
        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          Email Notifications
        </h3>
        
        <div class="space-y-4">
          <div class="group bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="flex items-start">
              <div class="flex items-center h-5 mt-0.5">
                <%= form.check_box :email_notifications, 
                                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200",
                                  checked: user.email_notifications.nil? ? true : user.email_notifications %>
              </div>
              <div class="ml-3 flex-1">
                <%= form.label :email_notifications, class: "block text-sm font-semibold text-gray-900 cursor-pointer" do %>
                  Account Activity Emails
                <% end %>
                <p class="text-sm text-gray-600 mt-1">Receive important notifications about your account activity via email</p>
              </div>
            </div>
          </div>

          <div class="group bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="flex items-start">
              <div class="flex items-center h-5 mt-0.5">
                <%= form.check_box :marketing_emails, 
                                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200" %>
              </div>
              <div class="ml-3 flex-1">
                <%= form.label :marketing_emails, class: "block text-sm font-semibold text-gray-900 cursor-pointer" do %>
                  Product Updates
                <% end %>
                <p class="text-sm text-gray-600 mt-1">Stay informed about new features, tips, and LinkMaster updates</p>
              </div>
            </div>
          </div>

          <div class="group bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="flex items-start">
              <div class="flex items-center h-5 mt-0.5">
                <%= form.check_box :weekly_reports, 
                                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200",
                                  checked: user.weekly_reports.nil? ? true : user.weekly_reports %>
              </div>
              <div class="ml-3 flex-1">
                <%= form.label :weekly_reports, class: "block text-sm font-semibold text-gray-900 cursor-pointer" do %>
                  Weekly Performance Reports
                <% end %>
                <p class="text-sm text-gray-600 mt-1">Get a comprehensive weekly summary of your link performance and analytics</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Real-time Notifications Section -->
      <div>
        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          Real-time Notifications
        </h3>
        
        <div class="group bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md hover:shadow-purple-500/5 transition-all duration-300">
          <div class="flex items-start">
            <div class="flex items-center h-5 mt-0.5">
              <%= form.check_box :real_time_alerts, 
                                class: "h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded transition-all duration-200" %>
            </div>
            <div class="ml-3 flex-1">
              <%= form.label :real_time_alerts, class: "block text-sm font-semibold text-gray-900 cursor-pointer" do %>
                Instant Activity Alerts
              <% end %>
              <p class="text-sm text-gray-600 mt-1">Get notified instantly when your links experience high-traffic spikes or unusual activity patterns</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Notification Categories Section -->
      <div>
        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
          <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
          </div>
          Notification Categories
        </h3>
        
        <div class="space-y-4">
          <div class="group bg-white rounded-xl border border-gray-200 p-5 hover:shadow-md hover:shadow-green-500/5 transition-all duration-300">
            <div class="flex items-center justify-between">
              <div class="flex-1 pr-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-1">Link Activity</h4>
                <p class="text-sm text-gray-600">Get notified about link clicks, performance milestones, and traffic patterns</p>
              </div>
              <div class="flex items-center gap-2">
                <label class="text-xs font-medium text-gray-500">Off</label>
                <button type="button" 
                        class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 hover:bg-gray-300"
                        data-controller="toggle"
                        data-action="click->toggle#toggle"
                        data-toggle-target="button"
                        aria-pressed="false">
                  <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out translate-x-0"></span>
                </button>
                <label class="text-xs font-medium text-gray-500">On</label>
              </div>
            </div>
          </div>

          <div class="group bg-white rounded-xl border border-gray-200 p-5 hover:shadow-md hover:shadow-red-500/5 transition-all duration-300">
            <div class="flex items-center justify-between">
              <div class="flex-1 pr-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-1">Account Security</h4>
                <p class="text-sm text-gray-600">Stay informed about login attempts, password changes, and security updates</p>
              </div>
              <div class="flex items-center gap-2">
                <label class="text-xs font-medium text-gray-500">Off</label>
                <button type="button" 
                        class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-green-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 hover:bg-green-700"
                        data-controller="toggle"
                        data-action="click->toggle#toggle"
                        data-toggle-target="button"
                        aria-pressed="true">
                  <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out translate-x-5"></span>
                </button>
                <label class="text-xs font-medium text-gray-500">On</label>
              </div>
            </div>
          </div>

          <div class="group bg-white rounded-xl border border-gray-200 p-5 hover:shadow-md hover:shadow-blue-500/5 transition-all duration-300">
            <div class="flex items-center justify-between">
              <div class="flex-1 pr-4">
                <h4 class="text-sm font-semibold text-gray-900 mb-1">Team Updates</h4>
                <p class="text-sm text-gray-600">Receive notifications about team member activity and collaboration updates</p>
              </div>
              <div class="flex items-center gap-2">
                <label class="text-xs font-medium text-gray-500">Off</label>
                <button type="button" 
                        class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:bg-gray-300"
                        data-controller="toggle"
                        data-action="click->toggle#toggle"
                        data-toggle-target="button"
                        aria-pressed="false">
                  <span class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out translate-x-0"></span>
                </button>
                <label class="text-xs font-medium text-gray-500">On</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Info Box -->
      <div class="p-4 bg-orange-50 border border-orange-200 rounded-xl">
        <div class="flex items-start gap-3">
          <div class="flex-shrink-0 mt-0.5">
            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="flex-1">
            <h4 class="text-sm font-semibold text-orange-900 mb-1">Smart Notifications</h4>
            <p class="text-sm text-orange-700">
              We use machine learning to send you only the most relevant notifications at the right time, 
              reducing notification fatigue while keeping you informed about what matters most.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex items-center justify-end gap-3">
      <%= link_to "Cancel", settings_path, 
                  class: "px-6 py-2.5 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors" %>
      <%= form.submit "Save Preferences", 
                     class: "px-6 py-2.5 bg-gradient-to-r from-orange-600 to-orange-700 text-white text-sm font-medium rounded-xl hover:from-orange-700 hover:to-orange-800 transition-all duration-200 shadow-lg shadow-orange-500/25 cursor-pointer",
                     data: { disable_with: "Saving..." } %>
    </div>
  <% end %>
</div>