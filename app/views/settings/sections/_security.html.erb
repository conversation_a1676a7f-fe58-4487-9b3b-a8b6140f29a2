<div id="security-settings">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Security Settings</h2>
      <p class="text-sm text-gray-600">Manage your account security and privacy preferences</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
      </svg>
    </div>
  </div>

  <% if local_assigns[:success] %>
    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <p class="text-sm font-medium text-green-800">Security settings updated successfully!</p>
      </div>
    </div>
  <% end %>

  <!-- Password Change Section -->
  <div class="mb-8">
    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
      <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
      </div>
      Change Password
    </h3>
    
    <%= form_with url: user_registration_path, method: :patch, local: true, data: { controller: "form-validation" } do |form| %>
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div class="sm:col-span-2">
          <%= form.label :current_password, class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <div class="relative">
            <%= form.password_field :current_password, 
                                   class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                   placeholder: "Enter current password",
                                   autocomplete: "current-password" %>
          </div>
        </div>

        <div>
          <%= form.label :password, "New Password", class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <%= form.password_field :password, 
                                 class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                 placeholder: "Enter new password",
                                 autocomplete: "new-password" %>
        </div>

        <div>
          <%= form.label :password_confirmation, "Confirm New Password", class: "block text-sm font-semibold text-gray-700 mb-2" %>
          <%= form.password_field :password_confirmation, 
                                 class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                                 placeholder: "Confirm new password",
                                 autocomplete: "new-password" %>
        </div>
      </div>

      <div class="mt-6">
        <%= form.submit "Update Password", 
                       class: "px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg shadow-blue-500/25 cursor-pointer",
                       data: { disable_with: "Updating..." } %>
      </div>
    <% end %>
  </div>

  <!-- Two-Factor Authentication Section -->
  <div class="mb-8">
    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
      <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
      </div>
      Two-Factor Authentication
    </h3>
    
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h4 class="text-base font-semibold text-gray-900 mb-1">Enhanced Security</h4>
          <p class="text-sm text-gray-600">Add an extra layer of security to your account</p>
        </div>
        <% if user.two_factor_enabled %>
          <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            Enabled
          </span>
        <% else %>
          <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-gray-100 text-gray-700 border border-gray-200">
            <svg class="w-3 h-3 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
            Disabled
          </span>
        <% end %>
      </div>

      <% if user.two_factor_enabled %>
        <p class="text-sm text-gray-600 mb-6">
          Two-factor authentication is currently enabled. You'll need your authenticator app to sign in.
        </p>
        <div class="flex flex-wrap gap-3">
          <button type="button" 
                  class="px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm font-medium rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 shadow-md shadow-red-500/25"
                  data-action="click->settings#disable2FA">
            Disable 2FA
          </button>
          <button type="button" 
                  class="px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white text-sm font-medium rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all duration-200 shadow-md shadow-gray-500/25"
                  data-action="click->settings#regenerateBackupCodes">
            Regenerate Backup Codes
          </button>
        </div>
      <% else %>
        <p class="text-sm text-gray-600 mb-6">
          Secure your account with two-factor authentication. You'll need to use an authenticator app like Google Authenticator or Authy.
        </p>
        <button type="button" 
                class="px-6 py-2.5 bg-gradient-to-r from-green-600 to-green-700 text-white text-sm font-medium rounded-xl hover:from-green-700 hover:to-green-800 transition-all duration-200 shadow-lg shadow-green-500/25"
                data-action="click->settings#enable2FA">
          Enable Two-Factor Authentication
        </button>
      <% end %>
    </div>
  </div>

  <!-- Session Management Section -->
  <div class="mb-8">
    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
      <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      Session Management
    </h3>
    
    <!-- Session Timeout -->
    <div class="mb-6">
      <%= form_with model: user, url: update_security_settings_path, method: :patch, local: true, data: { turbo_frame: "_top" } do |form| %>
        <%= form.label :session_timeout, "Auto Sign-Out", class: "block text-sm font-semibold text-gray-700 mb-2" %>
        <div class="relative">
          <%= form.select :session_timeout, 
                         options_for_select([
                           ['15 minutes', 15],
                           ['30 minutes', 30],
                           ['1 hour', 60],
                           ['4 hours', 240],
                           ['1 day', 1440],
                           ['Never', 0]
                         ], user.session_timeout || 60),
                         {},
                         { class: "w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 appearance-none bg-white" } %>
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        </div>
        <p class="mt-2 text-xs text-gray-500">How long before you're automatically signed out due to inactivity</p>
        
        <div class="mt-4">
          <%= form.submit "Update Session Settings", 
                         class: "px-6 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg shadow-purple-500/25 cursor-pointer",
                         data: { disable_with: "Updating..." } %>
        </div>
      <% end %>
    </div>

    <!-- Active Sessions -->
    <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-base font-semibold text-gray-900">Active Sessions</h4>
        <button type="button" 
                class="text-sm font-medium text-purple-600 hover:text-purple-700 transition-colors"
                data-action="click->settings#refreshSessions">
          <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh
        </button>
      </div>
      
      <div class="space-y-3" data-settings-target="sessionsList">
        <!-- Current Session -->
        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-200">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </div>
            </div>
            <div>
              <p class="text-sm font-semibold text-gray-900">Current Session</p>
              <p class="text-xs text-gray-600">Chrome on macOS • Last seen just now</p>
            </div>
          </div>
          <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
            Active
          </span>
        </div>

        <!-- Other Session -->
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200">
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gradient-to-br from-gray-400 to-gray-500 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path>
                  <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"></path>
                </svg>
              </div>
            </div>
            <div>
              <p class="text-sm font-semibold text-gray-900">Safari on iPhone</p>
              <p class="text-xs text-gray-600">Last seen 2 hours ago</p>
            </div>
          </div>
          <button type="button" 
                  class="text-sm font-medium text-red-600 hover:text-red-700 transition-colors"
                  data-action="click->settings#revokeSession">
            Revoke
          </button>
        </div>
      </div>

      <div class="mt-6 pt-6 border-t border-gray-200">
        <button type="button" 
                class="text-sm font-medium text-red-600 hover:text-red-700 transition-colors flex items-center gap-2"
                data-action="click->settings#revokeAllOtherSessions">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Sign out of all other sessions
        </button>
      </div>
    </div>
  </div>

  <!-- Privacy Settings Section -->
  <div>
    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
      <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"></path>
        </svg>
      </div>
      Privacy Settings
    </h3>
    
    <div class="space-y-4 mb-6">
      <div class="group bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md hover:shadow-orange-500/5 transition-all duration-300">
        <div class="flex items-start">
          <div class="flex items-center h-5 mt-0.5">
            <input type="checkbox" 
                   class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded transition-all duration-200"
                   checked>
          </div>
          <div class="ml-3 flex-1">
            <label class="block text-sm font-semibold text-gray-900 cursor-pointer">Analytics Tracking</label>
            <p class="text-sm text-gray-600 mt-1">Allow LinkMaster to track usage analytics to improve the service</p>
          </div>
        </div>
      </div>

      <div class="group bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md hover:shadow-orange-500/5 transition-all duration-300">
        <div class="flex items-start">
          <div class="flex items-center h-5 mt-0.5">
            <input type="checkbox" 
                   class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded transition-all duration-200">
          </div>
          <div class="ml-3 flex-1">
            <label class="block text-sm font-semibold text-gray-900 cursor-pointer">Automatic Data Export</label>
            <p class="text-sm text-gray-600 mt-1">Allow automatic data exports for backup purposes</p>
          </div>
        </div>
      </div>
    </div>

    <button type="button" 
            class="px-6 py-2.5 bg-gradient-to-r from-orange-600 to-orange-700 text-white text-sm font-medium rounded-xl hover:from-orange-700 hover:to-orange-800 transition-all duration-200 shadow-lg shadow-orange-500/25 flex items-center gap-2"
            data-action="click->settings#exportData">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
      </svg>
      Export My Data
    </button>
  </div>
</div>