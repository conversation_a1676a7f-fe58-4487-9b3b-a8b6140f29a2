<% content_for :title, "Team Settings" %>

<div class="p-6">
  <!-- Header Section -->
  <div class="mb-8">
    <nav class="flex items-center gap-2 text-sm text-gray-500 mb-4">
      <%= link_to settings_path, class: "hover:text-gray-700 transition-colors" do %>
        Settings
      <% end %>
      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
      <span class="text-gray-900 font-medium">Team</span>
    </nav>
    
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-1">Team Settings</h1>
        <p class="text-gray-600">Manage your team information and collaborate with others</p>
      </div>
      <div class="flex items-center gap-3">
        <%= link_to settings_path, 
                    class: "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Settings
        <% end %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Team Information Card -->
    <div class="lg:col-span-2 space-y-6">
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Team Information</h2>
            <p class="text-sm text-gray-600">Basic details about your team</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
        </div>

        <%= render 'team_settings/form', team: @team %>
      </div>

      <!-- Team Members Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Team Members</h2>
            <p class="text-sm text-gray-600">Manage who has access to your team's resources</p>
          </div>
          <button type="button" 
                  class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 shadow-lg shadow-purple-500/25"
                  data-action="click->team-settings#inviteMember">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Invite Member
          </button>
        </div>

        <div class="space-y-3" id="team-members-list">
          <% @team_memberships.each do |membership| %>
            <div class="group/member flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-200">
              <div class="flex items-center gap-4">
                <div class="relative">
                  <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center text-white font-bold shadow-lg group-hover/member:scale-110 transition-transform duration-200">
                    <%= membership.user.display_name.first.upcase %>
                  </div>
                  <% if membership.role == 'owner' %>
                    <div class="absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center">
                      <svg class="w-3 h-3 text-yellow-900" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    </div>
                  <% end %>
                </div>
                <div>
                  <p class="font-medium text-gray-900"><%= membership.user.display_name %></p>
                  <p class="text-sm text-gray-500"><%= membership.user.email %></p>
                </div>
              </div>
              
              <div class="flex items-center gap-3">
                <select class="px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all">
                  <option value="member" <%= 'selected' if membership.role == 'member' %>>Member</option>
                  <option value="admin" <%= 'selected' if membership.role == 'admin' %>>Admin</option>
                  <option value="owner" <%= 'selected' if membership.role == 'owner' %> <%= 'disabled' if membership.role == 'owner' %>>Owner</option>
                </select>
                
                <% unless membership.role == 'owner' %>
                  <button type="button" 
                          class="px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200"
                          data-action="click->team-settings#removeMember"
                          data-member-id="<%= membership.id %>">
                    Remove
                  </button>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>

        <% if @team_memberships.empty? %>
          <div class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-1">No team members yet</h3>
            <p class="text-gray-500">Invite your first team member to start collaborating</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Team Stats Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-gray-900">Team Stats</h3>
          <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <p class="text-3xl font-bold text-gray-900"><%= @team_memberships.count %></p>
            <p class="text-sm text-gray-500">Team members</p>
          </div>
          <div>
            <p class="text-3xl font-bold text-gray-900"><%= @team.links.count %></p>
            <p class="text-sm text-gray-500">Total links</p>
          </div>
          <div>
            <p class="text-3xl font-bold text-gray-900"><%= number_with_delimiter(@team.links.joins(:link_clicks).count) %></p>
            <p class="text-sm text-gray-500">Total clicks</p>
          </div>
        </div>
      </div>

      <!-- Team Permissions Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-yellow-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold text-gray-900">Permissions</h3>
          <div class="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Link Creation</label>
            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all">
              <option value="all">All members</option>
              <option value="admins">Admins only</option>
              <option value="owner">Owner only</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Link Editing</label>
            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all">
              <option value="creator">Link creator only</option>
              <option value="all">All members</option>
              <option value="admins">Admins only</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Analytics Access</label>
            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all">
              <option value="all">All members</option>
              <option value="admins">Admins only</option>
              <option value="owner">Owner only</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Danger Zone -->
      <div class="group bg-white rounded-2xl border border-red-200 p-6 hover:shadow-lg hover:shadow-red-500/5 transition-all duration-300">
        <div class="flex items-center gap-3 mb-4">
          <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-bold text-red-900">Danger Zone</h3>
        </div>
        
        <p class="text-sm text-gray-600 mb-4">
          Deleting a team will permanently remove all associated links and data. This action cannot be undone.
        </p>
        
        <button type="button"
                class="w-full px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white text-sm font-medium rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 shadow-lg shadow-red-500/25">
          Delete Team
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Invite Member Modal -->
<div id="invite-member-modal" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
  <div class="bg-white rounded-2xl shadow-xl max-w-md w-full mx-4">
    <div class="p-6">
      <h3 class="text-lg font-bold text-gray-900 mb-4">Invite Team Member</h3>
      <form>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
          <input type="email" 
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                 placeholder="<EMAIL>">
        </div>
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
          <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
            <option value="member">Member</option>
            <option value="admin">Admin</option>
          </select>
        </div>
        <div class="flex gap-3">
          <button type="button"
                  class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  data-action="click->team-settings#closeModal">
            Cancel
          </button>
          <button type="submit"
                  class="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200">
            Send Invite
          </button>
        </div>
      </form>
    </div>
  </div>
</div>