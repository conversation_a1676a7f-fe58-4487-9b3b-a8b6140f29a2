<% if local_assigns[:success] %>
  <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
    <div class="flex items-center gap-3">
      <div class="flex-shrink-0">
        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <p class="text-sm font-medium text-green-800">Team settings updated successfully!</p>
    </div>
  </div>
<% end %>

<%= form_with model: team, url: settings_team_settings_path, method: :patch, local: true, data: { turbo_frame: "_top" } do |form| %>
  <div class="space-y-6">
    <!-- Team Name -->
    <div>
      <%= form.label :name, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <%= form.text_field :name, 
                         class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400",
                         placeholder: "My Awesome Team" %>
      <% if team.errors[:name].any? %>
        <p class="mt-2 text-sm text-red-600 flex items-center gap-1">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <%= team.errors[:name].first %>
        </p>
      <% end %>
    </div>

    <!-- Team Description -->
    <div>
      <%= form.label :description, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <%= form.text_area :description, 
                        rows: 4,
                        class: "w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 placeholder-gray-400 resize-none",
                        placeholder: "Brief description of what your team does..." %>
      <p class="mt-2 text-xs text-gray-500">Help team members understand the team's purpose and goals</p>
    </div>

    <!-- Default Domain -->
    <div>
      <%= form.label :default_domain, class: "block text-sm font-semibold text-gray-700 mb-2" %>
      <div class="relative">
        <%= form.select :default_domain, 
                       options_for_select([
                         ['linkmaster.app (default)', ''],
                         ['Custom domain', 'custom']
                       ], team.default_domain),
                       { prompt: "Select default domain" },
                       { class: "w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 appearance-none bg-white" } %>
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>
      <p class="mt-2 text-xs text-gray-500">
        Default domain for new links created by team members. 
        <% if team.custom_domains.verified.any? %>
          You have <%= pluralize(team.custom_domains.verified.count, 'verified custom domain') %> available.
        <% else %>
          <%= link_to "Add a custom domain", settings_custom_domains_path, class: "text-blue-600 hover:text-blue-700 font-medium" %>
        <% end %>
      </p>
    </div>

    <!-- Team Settings Info -->
    <div class="p-4 bg-blue-50 border border-blue-200 rounded-xl">
      <div class="flex items-start gap-3">
        <div class="flex-shrink-0 mt-0.5">
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="flex-1">
          <h4 class="text-sm font-semibold text-blue-900 mb-1">Team Features</h4>
          <p class="text-sm text-blue-700">
            Teams enable collaboration on links, shared analytics, and centralized billing. 
            Team members can be assigned different roles with varying permissions.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="mt-8 flex items-center justify-end gap-3">
    <%= link_to "Cancel", settings_path, 
                class: "px-6 py-2.5 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors" %>
    <%= form.submit "Save Changes", 
                   class: "px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-lg shadow-blue-500/25 cursor-pointer",
                   data: { disable_with: "Saving..." } %>
  </div>
<% end %>