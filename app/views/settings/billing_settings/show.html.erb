<% content_for :title, "Billing Settings - LinkMaster" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
        <%= link_to settings_path, class: "hover:text-gray-700 transition-colors" do %>
          <span>Settings</span>
        <% end %>
        <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-gray-900 font-medium">Billing</span>
      </nav>
      
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Billing & Subscription
          </h1>
          <p class="text-gray-600">Manage your subscription plan and billing information</p>
        </div>
        <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Current Plan & Usage -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Current Plan Card -->
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-green-500/5 transition-all duration-300">
          <div class="px-8 py-6 bg-gradient-to-br from-green-50 to-emerald-50 border-b border-green-100">
            <h2 class="text-xl font-bold text-gray-900 flex items-center gap-2">
              <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              Current Plan
            </h2>
          </div>
          
          <div class="p-8">
            <div class="flex items-center justify-between mb-6">
              <div>
                <h3 class="text-2xl font-bold text-gray-900 capitalize mb-1"><%= @current_plan %> Plan</h3>
                <% current_plan_data = @subscription_plans.find { |p| p[:id] == @current_plan } %>
                <% if current_plan_data %>
                  <p class="text-lg text-gray-600">
                    <% if current_plan_data[:price] == 0 %>
                      <span class="font-semibold text-gray-900">Free</span> forever
                    <% else %>
                      <span class="font-bold text-gray-900 text-3xl">$<%= current_plan_data[:price] %></span>
                      <span class="text-gray-500">/month</span>
                    <% end %>
                  </p>
                <% end %>
              </div>
              <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200">
                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                Active
              </span>
            </div>

            <% if current_plan_data %>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <% current_plan_data[:features].each do |feature| %>
                  <div class="flex items-start gap-3">
                    <div class="flex-shrink-0 mt-0.5">
                      <div class="w-5 h-5 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                      </div>
                    </div>
                    <span class="text-sm text-gray-700"><%= feature %></span>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Usage Statistics Card -->
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
          <div class="px-8 py-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-b border-blue-100">
            <h2 class="text-xl font-bold text-gray-900 flex items-center gap-2">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              Usage This Month
            </h2>
          </div>
          
          <div class="p-8">
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-8">
              <!-- Links Created -->
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl mb-3">
                  <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                  </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-1"><%= @usage_stats[:links_created_this_month] %></div>
                <div class="text-sm font-medium text-gray-600">Links Created</div>
                <% if current_plan_data && current_plan_data[:links_limit] != Float::INFINITY %>
                  <div class="mt-3">
                    <div class="bg-gray-200 rounded-full h-2 overflow-hidden">
                      <% usage_percentage = (@usage_stats[:links_created_this_month].to_f / current_plan_data[:links_limit] * 100).round(1) %>
                      <div class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300" style="width: <%= [usage_percentage, 100].min %>%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-2 font-medium"><%= usage_percentage %>% of <%= number_with_delimiter(current_plan_data[:links_limit]) %></div>
                  </div>
                <% end %>
              </div>

              <!-- Total Clicks -->
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl mb-3">
                  <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"></path>
                  </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-1"><%= @usage_stats[:total_clicks_this_month] %></div>
                <div class="text-sm font-medium text-gray-600">Total Clicks</div>
              </div>

              <!-- API Requests -->
              <div class="text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-2xl mb-3">
                  <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <div class="text-3xl font-bold text-gray-900 mb-1"><%= @usage_stats[:api_requests_this_month] %></div>
                <div class="text-sm font-medium text-gray-600">API Requests</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Billing History Card -->
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
          <div class="px-8 py-6 bg-gradient-to-br from-purple-50 to-pink-50 border-b border-purple-100">
            <h2 class="text-xl font-bold text-gray-900 flex items-center gap-2">
              <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              Billing History
            </h2>
          </div>
          
          <div class="p-8">
            <div class="text-center py-12">
              <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mb-4">
                <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">No billing history</h3>
              <p class="text-gray-600 max-w-sm mx-auto">Your billing history will appear here once you upgrade to a paid plan.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Upgrade Plans & Payment -->
      <div class="space-y-6">
        <!-- Available Plans Card -->
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-indigo-500/5 transition-all duration-300">
          <div class="px-6 py-5 bg-gradient-to-br from-indigo-50 to-purple-50 border-b border-indigo-100">
            <h2 class="text-lg font-bold text-gray-900">Available Plans</h2>
          </div>
          
          <div class="p-6 space-y-4">
            <% @subscription_plans.each do |plan| %>
              <div class="relative rounded-xl border-2 p-5 transition-all duration-300 <%= plan[:id] == @current_plan ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-md shadow-blue-500/10' : 'border-gray-200 hover:border-gray-300 hover:shadow-md' %>">
                <% if plan[:id] == @current_plan %>
                  <div class="absolute -top-3 right-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-md">
                      Current Plan
                    </span>
                  </div>
                <% end %>
                
                <h3 class="font-bold text-gray-900 text-lg mb-2"><%= plan[:name] %></h3>
                
                <div class="mb-4">
                  <% if plan[:price] == 0 %>
                    <span class="text-3xl font-bold text-gray-900">Free</span>
                  <% else %>
                    <span class="text-3xl font-bold text-gray-900">$<%= plan[:price] %></span>
                    <span class="text-gray-500 text-sm">/month</span>
                  <% end %>
                </div>
                
                <ul class="space-y-2 mb-5">
                  <% plan[:features].each do |feature| %>
                    <li class="text-sm text-gray-700 flex items-start gap-2">
                      <svg class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      <span><%= feature %></span>
                    </li>
                  <% end %>
                </ul>
                
                <% if plan[:id] != @current_plan %>
                  <%= form_with model: @user, url: settings_billing_settings_path, method: :patch, local: true, data: { turbo_frame: "_top" } do |form| %>
                    <%= form.hidden_field :plan, value: plan[:id] %>
                    <% if plan[:price] == 0 %>
                      <%= form.submit "Downgrade", class: "w-full px-4 py-2.5 bg-gradient-to-r from-gray-600 to-gray-700 text-white text-sm font-medium rounded-xl hover:from-gray-700 hover:to-gray-800 transition-all duration-200 shadow-md shadow-gray-500/25 cursor-pointer" %>
                    <% else %>
                      <%= form.submit "Upgrade", class: "w-full px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white text-sm font-medium rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md shadow-blue-500/25 cursor-pointer" %>
                    <% end %>
                  <% end %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Payment Method Card -->
        <div class="group bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg hover:shadow-orange-500/5 transition-all duration-300">
          <div class="px-6 py-5 bg-gradient-to-br from-orange-50 to-amber-50 border-b border-orange-100">
            <h2 class="text-lg font-bold text-gray-900">Payment Method</h2>
          </div>
          
          <div class="p-6">
            <div class="text-center py-8">
              <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-100 to-amber-100 rounded-2xl mb-4">
                <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
              </div>
              <p class="text-gray-600 mb-6">No payment method on file</p>
              
              <button type="button" class="px-6 py-2.5 bg-gradient-to-r from-orange-600 to-amber-600 text-white text-sm font-medium rounded-xl hover:from-orange-700 hover:to-amber-700 transition-all duration-200 shadow-lg shadow-orange-500/25">
                Add Payment Method
              </button>
            </div>
          </div>
        </div>

        <!-- Need Help Card -->
        <div class="p-5 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="flex-1">
              <h3 class="font-semibold text-gray-900 mb-1">Need help?</h3>
              <p class="text-sm text-gray-600 mb-3">Contact our support team for billing questions</p>
              <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-700 transition-colors">
                Contact Support →
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>