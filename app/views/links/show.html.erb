<% content_for :title, @link.title || "Link Details" %>

<div class="p-6">
  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-1"><%= @link.title || "Link Details" %></h1>
        <p class="text-gray-600">Created <%= time_ago_in_words(@link.created_at) %> ago</p>
      </div>
      <div class="flex items-center gap-3">
        <%= link_to edit_link_path(@link), 
                    class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Link
        <% end %>
        <%= link_to links_path, 
                    class: "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          Back to Links
        <% end %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Link Details Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-purple-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">Link Information</h2>
            <p class="text-sm text-gray-600">Your shortened link details</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
            </svg>
          </div>
        </div>

        <!-- Short URL -->
        <div class="mb-6">
          <label class="block text-sm font-semibold text-gray-600 mb-2">Short URL</label>
          <div class="flex items-center gap-2">
            <div class="flex-1 relative">
              <input type="text" 
                     value="<%= @link.short_url(request) %>" 
                     readonly
                     class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-xl bg-gray-50 text-gray-900 font-mono text-sm"
                     id="short-url-<%= @link.id %>">
              <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              </div>
            </div>
            <button onclick="copyToClipboard('short-url-<%= @link.id %>')"
                    class="px-4 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 font-medium">
              Copy
            </button>
          </div>
        </div>

        <!-- Original URL -->
        <div class="mb-6">
          <label class="block text-sm font-semibold text-gray-600 mb-2">Original URL</label>
          <div class="p-4 bg-gray-50 rounded-xl">
            <%= link_to @link.original_url, @link.original_url, 
                        target: "_blank",
                        class: "text-blue-600 hover:text-blue-700 break-all text-sm flex items-center gap-2" %>
          </div>
        </div>

        <!-- Additional Info -->
        <div class="grid grid-cols-2 gap-4">
          <% if @link.custom_domain %>
            <div>
              <label class="block text-sm font-semibold text-gray-600 mb-1">Custom Domain</label>
              <p class="text-gray-900"><%= @link.custom_domain.domain %></p>
            </div>
          <% end %>
          <% if @link.team %>
            <div>
              <label class="block text-sm font-semibold text-gray-600 mb-1">Team</label>
              <p class="text-gray-900"><%= @link.team.name %></p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- QR Code Card -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-violet-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-1">QR Code</h2>
            <p class="text-sm text-gray-600">Share your link with a scannable code</p>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
            </svg>
          </div>
        </div>

        <div class="flex flex-col lg:flex-row items-start gap-6">
          <div class="mx-auto lg:mx-0">
            <div class="bg-white p-8 border-2 border-gray-200 rounded-xl shadow-sm qr-code-container">
              <%= raw @link.qr_code_svg(size: 8) %>
            </div>
          </div>
          <div class="flex-1">
            <p class="text-sm text-gray-600 mb-4">
              Scan this QR code with any smartphone camera to go directly to your short URL. Perfect for print materials, presentations, or anywhere you can't use a clickable link.
            </p>
            <div class="flex flex-col sm:flex-row gap-2">
              <%= link_to qr_code_link_path(@link, format: :svg), 
                          class: "inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",
                          data: { turbo: false } do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download SVG
              <% end %>
              <%= link_to qr_code_link_path(@link, format: :png), 
                          class: "inline-flex items-center justify-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-200",
                          data: { turbo: false } do %>
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                Download PNG
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="space-y-6">
      <!-- Stats Cards -->
      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-sm font-semibold text-gray-600 mb-1">TOTAL CLICKS</h3>
            <div class="w-8 h-1 bg-gradient-to-r from-blue-500 to-blue-400 rounded-full"></div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@link.link_clicks_count) %></div>
        <p class="text-sm text-gray-500">Total link clicks</p>
      </div>

      <div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-green-500/5 transition-all duration-300">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-sm font-semibold text-gray-600 mb-1">UNIQUE VISITORS</h3>
            <div class="w-8 h-1 bg-gradient-to-r from-green-500 to-green-400 rounded-full"></div>
          </div>
          <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
          </div>
        </div>
        <div class="text-3xl font-bold text-gray-900 mb-1"><%= number_with_delimiter(@link.unique_visitors) %></div>
        <p class="text-sm text-gray-500">Unique visitors</p>
      </div>

      <!-- Recent Clicks -->
      <% if @recent_clicks.any? %>
        <div class="bg-white rounded-2xl border border-gray-200 p-6">
          <h3 class="text-lg font-bold text-gray-900 mb-4">Recent Activity</h3>
          <div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-50 pr-2">
            <div class="space-y-3">
              <% @recent_clicks.each do |click| %>
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div class="flex-1">
                    <div class="flex items-center gap-2 text-sm">
                      <span class="font-medium text-gray-900">
                        <%= click.clicked_at.strftime("%b %d, %I:%M %p") %>
                      </span>
                      <% if click.tracking_data&.dig('country_code').present? %>
                        <span class="text-gray-600">•</span>
                        <span class="text-gray-700">
                          <%= click.tracking_data['country_code'] %>
                        </span>
                      <% end %>
                    </div>
                    <div class="flex items-center gap-2 mt-1">
                      <% if click.tracking_data&.dig('browser').present? %>
                        <span class="text-xs text-gray-500">
                          <%= click.tracking_data['browser'] %>
                        </span>
                      <% end %>
                      <% if click.attribution_data&.dig('utm_source').present? %>
                        <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-0.5 rounded-full">
                          <%= click.attribution_data['utm_source'] %>
                        </span>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
    button.classList.remove('from-purple-600', 'to-purple-700', 'hover:from-purple-700', 'hover:to-purple-800');
    
    setTimeout(() => {
      button.textContent = originalText;
      button.classList.remove('from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
      button.classList.add('from-purple-600', 'to-purple-700', 'hover:from-purple-700', 'hover:to-purple-800');
    }, 2000);
  }
</script>

<style>
  .qr-code-container {
    width: 280px;
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .qr-code-container svg {
    width: 100% !important;
    height: 100% !important;
    max-width: 100% !important;
  }
</style>