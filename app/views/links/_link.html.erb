<%= turbo_frame_tag link do %>
  <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition">
    <div class="flex items-start justify-between">
      <div class="flex-1">
        <div class="flex items-center space-x-3">
          <% if link.favicon_url.present? %>
            <%= image_tag link.favicon_url, class: "w-6 h-6", onerror: "this.style.display='none'" %>
          <% end %>
          <%= link_to link.title || link.original_url, link, 
                      class: "text-lg font-medium text-gray-900 hover:text-blue-600 truncate" %>
        </div>
        
        <div class="mt-2 flex items-center space-x-4 text-sm text-gray-500">
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            <%= link_to link.short_url, short_link_url(link.short_code), 
                        target: "_blank", 
                        class: "hover:text-blue-600" %>
          </span>
          
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            <%= pluralize(link.link_clicks_count, 'click') %>
          </span>
          
          <% if link.team %>
            <span class="flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <%= link.team.name %>
            </span>
          <% end %>
          
          <span>
            <%= time_ago_in_words(link.created_at) %> ago
          </span>
        </div>
      </div>
      
      <div class="ml-4 flex items-center space-x-2">
        <%= link_to edit_link_path(link), 
                    class: "text-gray-400 hover:text-gray-600",
                    data: { turbo_frame: "edit_link_modal" } do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        <% end %>
        
        <% if link.is_archived %>
          <%= button_to unarchive_link_path(link), 
                        method: :patch,
                        class: "text-gray-400 hover:text-green-600",
                        data: { turbo_method: :patch } do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          <% end %>
        <% else %>
          <%= button_to archive_link_path(link), 
                        method: :patch,
                        class: "text-gray-400 hover:text-yellow-600",
                        data: { turbo_method: :patch } do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
            </svg>
          <% end %>
        <% end %>
        
        <%= link_to qr_code_link_path(link, format: :png), 
                    class: "text-gray-400 hover:text-purple-600",
                    title: "Download QR Code",
                    data: { turbo: false } do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
          </svg>
        <% end %>
        
        <%= button_to link_path(link), 
                      method: :delete,
                      class: "text-gray-400 hover:text-red-600",
                      data: { turbo_method: :delete, turbo_confirm: "Are you sure?" } do %>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        <% end %>
      </div>
    </div>
  </div>
<% end %>