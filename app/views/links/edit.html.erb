<% content_for :title, "Edit Link" %>

<%= turbo_frame_tag "edit_link_modal" do %>
  <div class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity z-40" 
       data-controller="modal" 
       data-action="click->modal#closeOnBackdropClick keydown@document->modal#closeOnEscape"
       data-turbo-temporary>
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-2xl transition-all sm:my-8 sm:w-full sm:max-w-2xl" 
           data-action="click->modal#stopPropagation">
        <!-- Purple gradient header -->
        <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-8 sm:px-8">
          <div class="absolute top-0 right-0 pt-4 pr-4">
            <button type="button" class="text-white/80 hover:text-white transition-colors" data-action="click->modal#close">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-white/20 backdrop-blur rounded-2xl flex items-center justify-center">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-2xl font-bold text-white">Edit Link</h3>
              <p class="mt-1 text-purple-100">Update your link settings and details</p>
            </div>
          </div>
        </div>
        
        <!-- Form content -->
        <div class="px-6 py-6 sm:px-8">
          <%= render "form", link: @link %>
        </div>
      </div>
    </div>
  </div>
<% end %>