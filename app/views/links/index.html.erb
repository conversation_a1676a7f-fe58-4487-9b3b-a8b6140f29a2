<% content_for :page_title, "Links" %>
<% content_for :header_actions do %>
  <%= link_to new_link_path, class: "px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium flex items-center space-x-2", data: { turbo_frame: "new_link_modal" } do %>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
    </svg>
    <span>New Link</span>
  <% end %>
<% end %>

<div class="p-6">
  <!-- Search and Filters -->
  <div class="mb-6">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center space-x-4">
        <%= form_with url: links_path, method: :get, class: "flex items-center" do |f| %>
          <div class="relative">
            <%= f.text_field :q, 
                placeholder: "Search links...", 
                value: params[:q],
                class: "pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent w-64" %>
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        <% end %>
        
        <div class="flex items-center space-x-2">
          <%= link_to links_path, class: "px-3 py-2 text-sm font-medium rounded-lg #{!@links.first&.is_archived ? 'bg-purple-100 text-purple-700' : 'text-gray-700 hover:bg-gray-100'}" do %>
            Active
          <% end %>
          <%= link_to archived_links_path, class: "px-3 py-2 text-sm font-medium rounded-lg #{@links.first&.is_archived ? 'bg-purple-100 text-purple-700' : 'text-gray-700 hover:bg-gray-100'}" do %>
            Archived
          <% end %>
        </div>
      </div>
      
      <div class="text-sm text-gray-500">
        <%= @pagy.count %> links
      </div>
    </div>
  </div>

  <!-- Links Table -->
  <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Link
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Destination
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Clicks
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Created
          </th>
          <th scope="col" class="relative px-6 py-3">
            <span class="sr-only">Actions</span>
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @links.each do |link| %>
          <%= turbo_frame_tag link do %>
            <tr class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <% if link.favicon_url.present? %>
                    <%= image_tag link.favicon_url, class: "w-8 h-8 rounded mr-3", onerror: "this.style.display='none'" %>
                  <% else %>
                    <div class="w-8 h-8 bg-gray-200 rounded mr-3 flex items-center justify-center">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                      </svg>
                    </div>
                  <% end %>
                  <div>
                    <div class="text-sm font-medium text-gray-900">
                      <%= link_to link.short_code, short_link_url(link.short_code), target: "_blank", class: "hover:text-purple-600" %>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= link.title || "No title" %>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 truncate max-w-xs">
                  <%= link.original_url %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <span class="text-sm font-medium text-gray-900"><%= number_with_delimiter(link.link_clicks_count) %></span>
                  <svg class="w-4 h-4 text-gray-400 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <%= time_ago_in_words(link.created_at) %> ago
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <%= link_to analytic_path(link), class: "text-gray-400 hover:text-gray-600" do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                  <% end %>
                  
                  <%= link_to edit_link_path(link), class: "text-gray-400 hover:text-gray-600", data: { turbo_frame: "edit_link_modal" } do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  <% end %>
                  
                  <% if link.is_archived %>
                    <%= button_to unarchive_link_path(link), method: :patch, class: "text-gray-400 hover:text-green-600", data: { turbo_method: :patch } do %>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    <% end %>
                  <% else %>
                    <%= button_to archive_link_path(link), method: :patch, class: "text-gray-400 hover:text-yellow-600", data: { turbo_method: :patch } do %>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                      </svg>
                    <% end %>
                  <% end %>
                  
                  <%= button_to link_path(link), method: :delete, class: "text-gray-400 hover:text-red-600", data: { turbo_method: :delete, turbo_confirm: "Are you sure?" } do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        <% end %>
      </tbody>
    </table>
    
    <% if @links.empty? %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No links</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new link.</p>
        <div class="mt-6">
          <%= link_to new_link_path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700", data: { turbo_frame: "new_link_modal" } do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            New Link
          <% end %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Pagination -->
  <% if @pagy.pages > 1 %>
    <div class="mt-6">
      <%== pagy_nav(@pagy) %>
    </div>
  <% end %>
</div>

<!-- Modals -->
<%= turbo_frame_tag "new_link_modal" %>
<%= turbo_frame_tag "edit_link_modal" %>