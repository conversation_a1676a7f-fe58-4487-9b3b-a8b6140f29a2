<% content_for :title, "Create New Link" %>

<%= turbo_frame_tag "new_link_modal" do %>
  <div class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm transition-opacity z-40" 
       data-controller="modal" 
       data-action="click->modal#closeOnBackdropClick keydown@document->modal#closeOnEscape"
       data-turbo-temporary>
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-2xl transition-all sm:my-8 sm:w-full sm:max-w-2xl" 
           data-action="click->modal#stopPropagation">
        <!-- Purple gradient header -->
        <div class="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-8 sm:px-8">
          <div class="absolute top-0 right-0 pt-4 pr-4">
            <button type="button" class="text-white/80 hover:text-white transition-colors" data-action="click->modal#close">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-white/20 backdrop-blur rounded-2xl flex items-center justify-center">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-2xl font-bold text-white">Create Short Link</h3>
              <p class="mt-1 text-purple-100">Transform your long URL into a powerful, trackable link</p>
            </div>
          </div>
        </div>
        
        <!-- Features banner -->
        <div class="bg-purple-50 border-b border-purple-100 px-6 py-4 sm:px-8">
          <div class="flex items-center justify-between text-sm">
            <div class="flex items-center space-x-6">
              <div class="flex items-center space-x-2 text-purple-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="font-medium">Free Forever</span>
              </div>
              <div class="flex items-center space-x-2 text-purple-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <span class="font-medium">Real-time Analytics</span>
              </div>
              <div class="flex items-center space-x-2 text-purple-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <span class="font-medium">Secure & Private</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Form content -->
        <div class="px-6 py-6 sm:px-8">
          <div id="new_link_form">
            <%= render "form", link: @link %>
          </div>
          
          <!-- Tips section -->
          <div class="mt-6 bg-gray-50 rounded-xl p-4">
            <h4 class="text-sm font-semibold text-gray-700 mb-2">Pro Tips:</h4>
            <ul class="space-y-1 text-xs text-gray-600">
              <li class="flex items-start">
                <svg class="w-4 h-4 text-purple-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd" />
                </svg>
                <span>Custom back-halves make your links memorable and brandable</span>
              </li>
              <li class="flex items-start">
                <svg class="w-4 h-4 text-purple-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd" />
                </svg>
                <span>Track clicks, locations, and devices in real-time</span>
              </li>
              <li class="flex items-start">
                <svg class="w-4 h-4 text-purple-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd" />
                </svg>
                <span>QR codes generated automatically for each link</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>