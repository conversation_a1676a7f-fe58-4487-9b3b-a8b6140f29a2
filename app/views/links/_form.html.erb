<%= form_with model: link, local: false, data: { turbo_frame: "_top" } do |f| %>
  <% if link.errors.any? %>
    <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
      <ul class="list-disc list-inside">
        <% link.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="space-y-5">
    <div>
      <%= f.label :original_url, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
          </svg>
          <span>Destination URL</span>
        </div>
      <% end %>
      <%= f.text_field :original_url, 
                       placeholder: "https://example.com/very-long-url-that-needs-shortening", 
                       class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 placeholder-gray-400 transition-all duration-200",
                       required: true,
                       autofocus: true %>
    </div>

    <div>
      <%= f.label :custom_short_code, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
          </svg>
          <span>Custom back-half</span>
          <span class="text-xs font-normal text-gray-500">(optional)</span>
        </div>
      <% end %>
      <div class="flex">
        <span class="inline-flex items-center px-4 rounded-l-xl border border-r-0 border-gray-300 bg-gray-50 text-gray-600 text-sm font-medium">
          <%= request.base_url %>/
        </span>
        <%= f.text_field :custom_short_code, 
                         placeholder: "my-custom-link", 
                         class: "flex-1 px-4 py-3 border border-gray-300 rounded-r-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 placeholder-gray-400 transition-all duration-200" %>
      </div>
      <p class="mt-2 text-xs text-gray-500 flex items-center">
        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        Letters, numbers, hyphens and underscores only
      </p>
    </div>

    <% if @custom_domains&.any? %>
      <div>
        <%= f.label :custom_domain_id, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
            </svg>
            <span>Custom Domain</span>
            <span class="text-xs font-normal text-gray-500">(optional)</span>
          </div>
        <% end %>
        <%= f.select :custom_domain_id, 
                     options_for_select([["Default (linkmaster.app)", nil]] + @custom_domains.map { |d| [d.domain + (d.is_primary? ? " (Primary)" : ""), d.id] }, link.custom_domain_id),
                     {},
                     class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 transition-all duration-200" %>
        <p class="mt-2 text-xs text-gray-500 flex items-center">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          Links will use this domain instead of the default
        </p>
      </div>
    <% end %>

    <% if @teams&.any? %>
      <div>
        <%= f.label :team_id, class: "block text-sm font-semibold text-gray-700 mb-2" do %>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <span>Team</span>
            <span class="text-xs font-normal text-gray-500">(optional)</span>
          </div>
        <% end %>
        <%= f.select :team_id, 
                     options_for_select([["Personal", nil]] + @teams.map { |t| [t.name, t.id] }, link.team_id),
                     {},
                     class: "w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-900 transition-all duration-200" %>
      </div>
    <% end %>

    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-100">
      <%= link_to "Cancel", "#", class: "px-6 py-2.5 border border-gray-300 rounded-xl text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200", data: { action: "click->modal#close" } %>
      <%= f.submit link.persisted? ? "Update Link" : "Create Short Link", 
                   class: "px-6 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md" %>
    </div>
  </div>
<% end %>