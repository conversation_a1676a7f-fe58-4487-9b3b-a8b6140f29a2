<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 py-24 px-4 sm:px-6 lg:px-8">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
  </div>
  
  <div class="relative max-w-4xl mx-auto text-center">
    <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
      How Can We <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Help You?</span>
    </h1>
    <p class="text-xl text-blue-100 max-w-2xl mx-auto mb-8">
      Find answers to common questions, explore our documentation, or contact our support team.
    </p>
    
    <!-- Search Bar -->
    <div class="max-w-2xl mx-auto">
      <div class="relative">
        <input type="text" 
               placeholder="Search for help articles..." 
               class="w-full px-6 py-4 pr-12 text-lg border-0 rounded-2xl shadow-xl focus:ring-4 focus:ring-blue-400 focus:outline-none">
        <button class="absolute inset-y-0 right-0 flex items-center pr-4">
          <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </button>
      </div>
      
      <!-- Popular Searches -->
      <div class="mt-4 flex flex-wrap justify-center gap-2">
        <span class="text-blue-200 text-sm">Popular:</span>
        <button class="text-blue-300 hover:text-white text-sm underline">Custom domains</button>
        <button class="text-blue-300 hover:text-white text-sm underline">API documentation</button>
        <button class="text-blue-300 hover:text-white text-sm underline">Billing</button>
        <button class="text-blue-300 hover:text-white text-sm underline">Analytics</button>
      </div>
    </div>
  </div>
</div>

<!-- Main Categories -->
<div class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid md:grid-cols-3 gap-8">
      <!-- Getting Started -->
      <div class="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
        <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Getting Started</h3>
        <p class="text-gray-600 mb-6">New to LinkMaster? Start here to learn the basics.</p>
        <ul class="space-y-3">
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Quick Start Guide <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Creating Your First Link <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Understanding Analytics <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Video Tutorials <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
        </ul>
      </div>
      
      <!-- Features & Tools -->
      <div class="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
        <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Features & Tools</h3>
        <p class="text-gray-600 mb-6">Learn how to use LinkMaster's powerful features.</p>
        <ul class="space-y-3">
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Custom Domains <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">QR Code Generator <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Link Retargeting <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Team Collaboration <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
        </ul>
      </div>
      
      <!-- Account & Billing -->
      <div class="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow">
        <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-4">Account & Billing</h3>
        <p class="text-gray-600 mb-6">Manage your account, subscription, and billing.</p>
        <ul class="space-y-3">
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Upgrade Your Plan <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Payment Methods <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Invoices & Receipts <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
          <li><a href="#" class="text-blue-600 hover:text-blue-700 flex items-center">Cancel Subscription <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg></a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<!-- Popular Articles -->
<div class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <h2 class="text-3xl font-bold text-gray-900 mb-8">Popular Help Articles</h2>
    
    <div class="grid md:grid-cols-2 gap-6">
      <article class="border border-gray-200 rounded-xl p-6 hover:border-purple-300 hover:shadow-lg transition-all">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">How to Set Up Custom Domains</h3>
            <p class="text-gray-600 text-sm mb-3">Learn how to configure your own domain for branded short links.</p>
            <a href="#" class="text-purple-600 hover:text-purple-700 text-sm font-medium">Read Article →</a>
          </div>
        </div>
      </article>
      
      <article class="border border-gray-200 rounded-xl p-6 hover:border-purple-300 hover:shadow-lg transition-all">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Understanding Your Analytics Dashboard</h3>
            <p class="text-gray-600 text-sm mb-3">A complete guide to tracking and interpreting your link performance.</p>
            <a href="#" class="text-purple-600 hover:text-purple-700 text-sm font-medium">Read Article →</a>
          </div>
        </div>
      </article>
      
      <article class="border border-gray-200 rounded-xl p-6 hover:border-purple-300 hover:shadow-lg transition-all">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">API Authentication Guide</h3>
            <p class="text-gray-600 text-sm mb-3">Set up API keys and authenticate your requests securely.</p>
            <a href="#" class="text-purple-600 hover:text-purple-700 text-sm font-medium">Read Article →</a>
          </div>
        </div>
      </article>
      
      <article class="border border-gray-200 rounded-xl p-6 hover:border-purple-300 hover:shadow-lg transition-all">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Team Management & Permissions</h3>
            <p class="text-gray-600 text-sm mb-3">Learn how to add team members and manage their access levels.</p>
            <a href="#" class="text-purple-600 hover:text-purple-700 text-sm font-medium">Read Article →</a>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>

<!-- Contact Support -->
<div class="py-16 bg-gradient-to-br from-purple-900 to-pink-900">
  <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-3xl font-bold text-white mb-4">Still Need Help?</h2>
    <p class="text-xl text-purple-100 mb-8">
      Our support team is here to help you 24/7.
    </p>
    
    <div class="grid md:grid-cols-3 gap-8">
      <!-- Live Chat -->
      <div class="text-center">
        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-white mb-2">Live Chat</h3>
        <p class="text-purple-100 mb-4">Chat with our support team in real-time.</p>
        <button class="bg-white text-purple-900 px-6 py-2 rounded-lg font-medium hover:bg-purple-50 transition-colors">
          Start Chat
        </button>
      </div>
      
      <!-- Email Support -->
      <div class="text-center">
        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-white mb-2">Email Support</h3>
        <p class="text-purple-100 mb-4">Get help via email within 24 hours.</p>
        <a href="mailto:<EMAIL>" class="bg-white text-purple-900 px-6 py-2 rounded-lg font-medium hover:bg-purple-50 transition-colors inline-block">
          Send Email
        </a>
      </div>
      
      <!-- Priority Support -->
      <div class="text-center">
        <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-white mb-2">Priority Support</h3>
        <p class="text-purple-100 mb-4">For Enterprise customers only.</p>
        <button class="bg-white/20 backdrop-blur-sm text-white px-6 py-2 rounded-lg font-medium border border-white/30 hover:bg-white/30 transition-colors">
          Call +1 (555) 123-4567
        </button>
      </div>
    </div>
  </div>
</div>