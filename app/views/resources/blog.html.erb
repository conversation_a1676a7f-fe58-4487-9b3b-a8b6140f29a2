<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-24 px-4 sm:px-6 lg:px-8">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
  </div>
  
  <div class="relative max-w-4xl mx-auto text-center">
    <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
      LinkMaster <span class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Blog</span>
    </h1>
    <p class="text-xl text-purple-100 max-w-2xl mx-auto leading-relaxed">
      Insights, tutorials, and best practices for modern link management and digital marketing.
    </p>
  </div>
</div>

<!-- Filter and Search Section -->
<div class="py-12 bg-gray-50" data-controller="blog">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <!-- Categories Filter -->
      <div class="flex flex-wrap gap-3">
        <button data-blog-target="categoryButton" 
                data-category="all"
                data-action="click->blog#filterByCategory"
                class="px-4 py-2 bg-purple-600 text-white rounded-full text-sm font-medium hover:bg-purple-700 transition-colors">
          All Posts
        </button>
        <button data-blog-target="categoryButton" 
                data-category="tutorials"
                data-action="click->blog#filterByCategory"
                class="px-4 py-2 bg-white text-gray-600 rounded-full text-sm font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
          Tutorials
        </button>
        <button data-blog-target="categoryButton" 
                data-category="product-updates"
                data-action="click->blog#filterByCategory"
                class="px-4 py-2 bg-white text-gray-600 rounded-full text-sm font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
          Product Updates
        </button>
        <button data-blog-target="categoryButton" 
                data-category="marketing-tips"
                data-action="click->blog#filterByCategory"
                class="px-4 py-2 bg-white text-gray-600 rounded-full text-sm font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
          Marketing Tips
        </button>
        <button data-blog-target="categoryButton" 
                data-category="case-studies"
                data-action="click->blog#filterByCategory"
                class="px-4 py-2 bg-white text-gray-600 rounded-full text-sm font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
          Case Studies
        </button>
      </div>
      
      <!-- Search Bar -->
      <div class="relative">
        <input type="text" 
               placeholder="Search articles..." 
               data-blog-target="searchInput"
               data-action="input->blog#search"
               class="w-full lg:w-80 px-4 py-2 pr-10 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent">
        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>

<!-- Featured Article Section -->
<% if @featured_post %>
<div class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Article</h2>
    </div>
    
    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl overflow-hidden shadow-xl">
      <div class="lg:grid lg:grid-cols-2 lg:gap-8">
        <!-- Media Content -->
        <div class="relative">
          <% case @featured_post.media_type %>
          <% when 'video' %>
            <div class="aspect-video bg-gray-900 flex items-center justify-center video-container">
              <video class="w-full h-full object-cover" poster="<%= @featured_post.media_url %>">
                <source src="#" type="video/mp4">
              </video>
              <div class="absolute inset-0 flex items-center justify-center play-button" data-action="click->blog#playVideo">
                <div class="w-20 h-20 bg-white/90 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                  <svg class="w-8 h-8 text-purple-600 ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
          <% else %>
            <img src="<%= @featured_post.media_url %>" 
                 alt="<%= @featured_post.title %>" 
                 class="w-full h-full object-cover">
          <% end %>
          
          <!-- Media Type Badge -->
          <% if @featured_post.media_type %>
            <div class="absolute top-4 left-4">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= media_type_badge_classes(@featured_post.media_type) %>">
                <%= media_type_icon(@featured_post.media_type) %>
                <%= media_type_label(@featured_post.media_type) %>
              </span>
            </div>
          <% end %>
        </div>
        
        <!-- Content -->
        <div class="p-8 lg:p-12">
          <div class="flex items-center space-x-4 mb-4">
            <span class="text-purple-600 text-sm font-medium"><%= @featured_post.category %></span>
            <span class="text-gray-300">•</span>
            <span class="text-gray-500 text-sm"><%= @featured_post.published_at.strftime('%B %d, %Y') %></span>
          </div>
          
          <h3 class="text-3xl font-bold text-gray-900 mb-4">
            <%= @featured_post.title %>
          </h3>
          
          <p class="text-lg text-gray-600 mb-6">
            <%= @featured_post.excerpt(200) %>
          </p>
          
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <% if @featured_post.author_avatar.present? %>
                <img src="<%= @featured_post.author_avatar %>" 
                     alt="<%= @featured_post.author %>" 
                     class="w-10 h-10 rounded-full object-cover">
              <% else %>
                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
                  <%= @featured_post.author.split(' ').map(&:first).join %>
                </div>
              <% end %>
              <div>
                <p class="font-medium text-gray-900"><%= @featured_post.author %></p>
                <p class="text-gray-500 text-sm"><%= @featured_post.author_title %></p>
              </div>
            </div>
            
            <%= link_to "Read More", blog_post_path(id: @featured_post.slug), class: "bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200 inline-block" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<% end %>

<!-- Blog Posts Grid -->
<div class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- No Results Message -->
    <div data-blog-target="noResults" class="hidden text-center py-12">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No articles found</h3>
      <p class="text-gray-600">Try adjusting your search or filters</p>
    </div>
    
    <div class="grid lg:grid-cols-3 gap-8" id="blog-posts-container">
      <% @posts.each do |post| %>
        <%= render 'blog_post_card', post: post %>
      <% end %>
    </div>
    
    <!-- Load More Button -->
    <div class="text-center mt-12">
      <button data-blog-target="loadMoreButton"
              data-action="click->blog#loadMore"
              class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200">
        Load More Articles
      </button>
    </div>
  </div>
</div>

<!-- Newsletter Subscription -->
<div class="py-16 bg-white">
  <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-3xl font-bold text-gray-900 mb-4">Stay Updated</h2>
    <p class="text-xl text-gray-600 mb-8">
      Get the latest insights, tutorials, and product updates delivered to your inbox.
    </p>
    <form data-action="submit->blog#subscribe" class="flex flex-col sm:flex-row gap-4 justify-center max-w-lg mx-auto">
      <input type="email" 
             placeholder="Enter your email" 
             required
             class="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent">
      <button type="submit" class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-200">
        Subscribe
      </button>
    </form>
  </div>
</div>