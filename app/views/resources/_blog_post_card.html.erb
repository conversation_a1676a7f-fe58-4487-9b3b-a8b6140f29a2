<article data-blog-target="article" 
         data-category="<%= post.category&.parameterize %>"
         data-author="<%= post.author %>"
         class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
  <div class="relative">
    <% case post.media_type %>
    <% when 'video' %>
      <div class="aspect-video bg-gray-900 rounded-t-2xl overflow-hidden">
        <img src="<%= post.media_url %>" 
             alt="<%= post.title %>" 
             class="w-full h-full object-cover">
        <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
          <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-gray-900 ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>
        <% if post.video_duration %>
          <div class="absolute bottom-4 right-4">
            <span class="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
              <%= post.video_duration %>
            </span>
          </div>
        <% end %>
      </div>
    <% when 'infographic', 'image' %>
      <img src="<%= post.media_url %>" 
           alt="<%= post.title %>" 
           class="w-full h-48 object-cover">
    <% when 'podcast' %>
      <div class="relative bg-gradient-to-br from-emerald-500 to-teal-600 h-48 flex items-center justify-center">
        <div class="text-center text-white">
          <svg class="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 3a9 9 0 019 9 9 9 0 01-9 9 9 9 0 01-9-9 9 9 0 019-9zm0 2a7 7 0 00-7 7 7 7 0 007 7 7 7 0 007-7 7 7 0 00-7-7zm0 2a5 5 0 015 5 5 5 0 01-5 5 5 5 0 01-5-5 5 5 0 015-5zm0 2a3 3 0 00-3 3 3 3 0 003 3 3 3 0 003-3 3 3 0 00-3-3z"/>
          </svg>
          <% if post.episode_number %>
            <p class="font-semibold">Episode <%= post.episode_number %></p>
          <% end %>
          <p class="text-emerald-100 text-sm"><%= post.reading_time %></p>
        </div>
      </div>
    <% when 'pdf' %>
      <div class="relative bg-gradient-to-br from-orange-500 to-red-600 h-48 flex items-center justify-center">
        <div class="text-center text-white">
          <svg class="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
          </svg>
          <p class="font-semibold">Research Report</p>
          <p class="text-orange-100 text-sm">PDF Download</p>
        </div>
      </div>
    <% else %>
      <div class="h-48 bg-gradient-to-br from-purple-500 to-pink-600"></div>
    <% end %>
    
    <!-- Media Type Badge -->
    <% if post.media_type %>
      <div class="absolute top-4 left-4">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium <%= media_type_badge_classes(post.media_type) %>">
          <%= media_type_icon(post.media_type) %>
          <%= media_type_label(post.media_type) %>
        </span>
      </div>
    <% end %>
  </div>
  
  <div class="p-6">
    <div class="flex items-center space-x-4 mb-3">
      <span class="text-purple-600 text-sm font-medium"><%= post.category %></span>
      <span class="text-gray-300">•</span>
      <span class="text-gray-500 text-sm"><%= post.reading_time %></span>
    </div>
    
    <%= link_to blog_post_path(id: post.slug), class: "block group" do %>
      <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors">
        <%= post.title %>
      </h3>
    <% end %>
    
    <p class="text-gray-600 text-sm mb-4">
      <%= post.excerpt %>
    </p>
    
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <% if post.author_avatar.present? %>
          <img src="<%= post.author_avatar %>" 
               alt="<%= post.author %>" 
               class="w-8 h-8 rounded-full object-cover">
        <% else %>
          <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
            <%= post.author.split(' ').map(&:first).join %>
          </div>
        <% end %>
        <span class="text-gray-700 text-sm font-medium"><%= post.author %></span>
      </div>
      <span class="text-gray-500 text-sm"><%= post.published_at.strftime('%b %d') %></span>
    </div>
  </div>
</article>