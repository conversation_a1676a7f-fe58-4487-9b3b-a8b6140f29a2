<!-- Hero Section -->
<div class="relative bg-gradient-to-br from-emerald-900 via-teal-900 to-cyan-900 py-24 px-4 sm:px-6 lg:px-8">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
  </div>
  
  <div class="relative max-w-4xl mx-auto text-center">
    <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
      Complete <span class="bg-gradient-to-r from-emerald-400 to-cyan-400 bg-clip-text text-transparent">Guides</span>
    </h1>
    <p class="text-xl text-emerald-100 max-w-2xl mx-auto">
      Step-by-step tutorials and comprehensive guides to master LinkMaster and boost your marketing campaigns.
    </p>
  </div>
</div>

<!-- Guide Categories -->
<div class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Category Tabs -->
    <div class="flex flex-wrap justify-center gap-4 mb-12">
      <button class="px-6 py-3 bg-emerald-600 text-white rounded-full font-medium hover:bg-emerald-700 transition-colors">
        All Guides
      </button>
      <button class="px-6 py-3 bg-white text-gray-600 rounded-full font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
        Getting Started
      </button>
      <button class="px-6 py-3 bg-white text-gray-600 rounded-full font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
        Advanced Features
      </button>
      <button class="px-6 py-3 bg-white text-gray-600 rounded-full font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
        API & Integration
      </button>
      <button class="px-6 py-3 bg-white text-gray-600 rounded-full font-medium hover:bg-gray-100 border border-gray-200 transition-colors">
        Best Practices
      </button>
    </div>

    <!-- Featured Guide -->
    <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl overflow-hidden shadow-xl mb-12">
      <div class="grid lg:grid-cols-2 gap-8">
        <div class="p-8 lg:p-12">
          <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 mb-4">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
            </svg>
            Featured Guide
          </div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            The Complete Guide to Link Analytics
          </h2>
          <p class="text-lg text-gray-600 mb-6">
            Master LinkMaster's analytics dashboard and learn how to track, measure, and optimize your link performance for maximum ROI.
          </p>
          <ul class="space-y-2 mb-8">
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Understanding key metrics and KPIs
            </li>
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Setting up conversion tracking
            </li>
            <li class="flex items-center text-gray-700">
              <svg class="w-5 h-5 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Creating custom reports
            </li>
          </ul>
          <div class="flex items-center space-x-4">
            <a href="#" class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200">
              Read Guide
            </a>
            <span class="text-gray-500">25 min read</span>
          </div>
        </div>
        <div class="relative h-full min-h-[400px]">
          <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Analytics Dashboard" 
               class="absolute inset-0 w-full h-full object-cover">
        </div>
      </div>
    </div>

    <!-- Guide Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Guide 1 -->
      <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Getting Started
            </span>
            <span class="text-gray-500 text-sm">15 min read</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">
            Quick Start: Your First Short Link
          </h3>
          <p class="text-gray-600 mb-4">
            Learn how to create, customize, and share your first shortened link with LinkMaster in under 5 minutes.
          </p>
          <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
            Read Guide
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </article>

      <!-- Guide 2 -->
      <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              Advanced Features
            </span>
            <span class="text-gray-500 text-sm">20 min read</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">
            Mastering Custom Domains
          </h3>
          <p class="text-gray-600 mb-4">
            Set up and configure custom domains for your branded links, including SSL certificates and DNS settings.
          </p>
          <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
            Read Guide
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </article>

      <!-- Guide 3 -->
      <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              API & Integration
            </span>
            <span class="text-gray-500 text-sm">30 min read</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">
            API Integration Guide
          </h3>
          <p class="text-gray-600 mb-4">
            Complete guide to integrating LinkMaster's API into your applications with code examples and best practices.
          </p>
          <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
            Read Guide
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </article>

      <!-- Guide 4 -->
      <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
              Best Practices
            </span>
            <span class="text-gray-500 text-sm">18 min read</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">
            Link Management Best Practices
          </h3>
          <p class="text-gray-600 mb-4">
            Learn proven strategies for organizing, naming, and managing your links for maximum efficiency.
          </p>
          <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
            Read Guide
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </article>

      <!-- Guide 5 -->
      <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800">
              Advanced Features
            </span>
            <span class="text-gray-500 text-sm">22 min read</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">
            QR Code Marketing Guide
          </h3>
          <p class="text-gray-600 mb-4">
            Everything you need to know about creating and using QR codes for offline-to-online marketing campaigns.
          </p>
          <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
            Read Guide
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </article>

      <!-- Guide 6 -->
      <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
        <div class="p-6">
          <div class="flex items-center space-x-2 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
              Best Practices
            </span>
            <span class="text-gray-500 text-sm">25 min read</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-3">
            Team Collaboration Playbook
          </h3>
          <p class="text-gray-600 mb-4">
            Set up your team for success with roles, permissions, and collaborative workflows in LinkMaster.
          </p>
          <a href="#" class="text-emerald-600 hover:text-emerald-700 font-medium flex items-center">
            Read Guide
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </article>
    </div>

    <!-- Load More -->
    <div class="text-center mt-12">
      <button class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-200">
        Load More Guides
      </button>
    </div>
  </div>
</div>

<!-- Video Tutorials Section -->
<div class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">Video Tutorials</h2>
      <p class="text-xl text-gray-600">Learn by watching. Quick video guides for visual learners.</p>
    </div>

    <div class="grid md:grid-cols-3 gap-8">
      <!-- Video 1 -->
      <div class="bg-gray-50 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
        <div class="relative aspect-video bg-gray-900">
          <img src="https://images.unsplash.com/photo-1611162617474-5b21e879e113?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Video tutorial thumbnail" 
               class="w-full h-full object-cover">
          <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <button class="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
              <svg class="w-6 h-6 text-gray-900 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </button>
          </div>
          <div class="absolute bottom-2 right-2">
            <span class="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
              5:42
            </span>
          </div>
        </div>
        <div class="p-4">
          <h3 class="font-semibold text-gray-900 mb-2">Getting Started with LinkMaster</h3>
          <p class="text-gray-600 text-sm">A quick introduction to LinkMaster's interface and basic features.</p>
        </div>
      </div>

      <!-- Video 2 -->
      <div class="bg-gray-50 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
        <div class="relative aspect-video bg-gray-900">
          <img src="https://images.unsplash.com/photo-1633356122544-f134324a6cee?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Video tutorial thumbnail" 
               class="w-full h-full object-cover">
          <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <button class="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
              <svg class="w-6 h-6 text-gray-900 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </button>
          </div>
          <div class="absolute bottom-2 right-2">
            <span class="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
              8:15
            </span>
          </div>
        </div>
        <div class="p-4">
          <h3 class="font-semibold text-gray-900 mb-2">Custom Domain Setup</h3>
          <p class="text-gray-600 text-sm">Step-by-step guide to setting up your custom branded domain.</p>
        </div>
      </div>

      <!-- Video 3 -->
      <div class="bg-gray-50 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
        <div class="relative aspect-video bg-gray-900">
          <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Video tutorial thumbnail" 
               class="w-full h-full object-cover">
          <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <button class="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
              <svg class="w-6 h-6 text-gray-900 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </button>
          </div>
          <div class="absolute bottom-2 right-2">
            <span class="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
              12:30
            </span>
          </div>
        </div>
        <div class="p-4">
          <h3 class="font-semibold text-gray-900 mb-2">Analytics Deep Dive</h3>
          <p class="text-gray-600 text-sm">Master LinkMaster's analytics to track and optimize your campaigns.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Newsletter CTA -->
<div class="py-16 bg-gradient-to-br from-emerald-900 to-teal-900">
  <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-3xl font-bold text-white mb-4">Want More Guides?</h2>
    <p class="text-xl text-emerald-100 mb-8">
      Get our latest tutorials and tips delivered to your inbox every week.
    </p>
    <form class="flex flex-col sm:flex-row gap-4 justify-center max-w-lg mx-auto">
      <input type="email" 
             placeholder="Enter your email" 
             required
             class="flex-1 px-4 py-3 border border-emerald-300 rounded-xl focus:ring-2 focus:ring-white focus:border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-emerald-300">
      <button type="submit" class="bg-white text-emerald-900 px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-200">
        Subscribe
      </button>
    </form>
  </div>
</div>