<!-- Article Header -->
<div class="relative bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-16 px-4 sm:px-6 lg:px-8">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
  </div>
  
  <div class="relative max-w-4xl mx-auto">
    <!-- Breadcrumb -->
    <nav class="mb-8">
      <ol class="flex items-center space-x-2 text-sm">
        <li>
          <%= link_to root_path, class: "text-purple-300 hover:text-white transition-colors" do %>
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </svg>
          <% end %>
        </li>
        <li class="text-purple-400">/</li>
        <li>
          <%= link_to "Blog", blog_path, class: "text-purple-300 hover:text-white transition-colors" %>
        </li>
        <li class="text-purple-400">/</li>
        <li class="text-purple-100 truncate max-w-xs">
          <%= @post.title %>
        </li>
      </ol>
    </nav>

    <!-- Article Meta -->
    <div class="flex items-center space-x-4 mb-6 text-sm">
      <span class="inline-flex items-center px-3 py-1 rounded-full bg-purple-800/50 text-purple-200">
        <%= @post.category %>
      </span>
      <span class="text-purple-300">•</span>
      <span class="text-purple-300"><%= @post.published_at.strftime('%B %d, %Y') %></span>
      <span class="text-purple-300">•</span>
      <span class="text-purple-300"><%= @post.reading_time %></span>
    </div>

    <!-- Title -->
    <h1 class="text-3xl md:text-5xl font-bold text-white mb-8 leading-tight">
      <%= @post.title %>
    </h1>

    <!-- Author -->
    <div class="flex items-center space-x-4">
      <% if @post.author_avatar %>
        <img src="<%= @post.author_avatar %>" 
             alt="<%= @post.author %>" 
             class="w-12 h-12 rounded-full object-cover border-2 border-purple-400">
      <% else %>
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
          <%= @post.author.split(' ').map(&:first).join %>
        </div>
      <% end %>
      <div>
        <p class="font-semibold text-white"><%= @post.author %></p>
        <p class="text-purple-300 text-sm"><%= @post.author_title %></p>
      </div>
    </div>
  </div>
</div>

<!-- Article Content -->
<article class="py-12 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Featured Media -->
    <% case @post.media_type %>
    <% when 'video' %>
      <div class="mb-12 -mt-24 relative z-10">
        <div class="aspect-video bg-gray-900 rounded-2xl overflow-hidden shadow-2xl">
          <img src="<%= @post.media_url %>" 
               alt="Video thumbnail" 
               class="w-full h-full object-cover">
          <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <button class="w-20 h-20 bg-white/90 rounded-full flex items-center justify-center hover:scale-110 transition-transform">
              <svg class="w-8 h-8 text-purple-600 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </button>
          </div>
          <% if @post.video_duration %>
            <div class="absolute bottom-4 right-4">
              <span class="bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm">
                <%= @post.video_duration %>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    <% when 'infographic' %>
      <div class="mb-12 -mt-24 relative z-10">
        <div class="rounded-2xl overflow-hidden shadow-2xl">
          <img src="<%= @post.media_url %>" 
               alt="Infographic" 
               class="w-full">
        </div>
      </div>
    <% when 'podcast' %>
      <div class="mb-12 -mt-24 relative z-10">
        <div class="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl p-8 shadow-2xl">
          <div class="text-center text-white mb-6">
            <svg class="w-20 h-20 mx-auto mb-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 3a9 9 0 019 9 9 9 0 01-9 9 9 9 0 01-9-9 9 9 0 019-9zm0 2a7 7 0 00-7 7 7 7 0 007 7 7 7 0 007-7 7 7 0 00-7-7zm0 2a5 5 0 015 5 5 5 0 01-5 5 5 5 0 01-5-5 5 5 0 015-5zm0 2a3 3 0 00-3 3 3 3 0 003 3 3 3 0 003-3 3 3 0 00-3-3z"/>
            </svg>
            <% if @post.episode_number %>
              <p class="text-2xl font-bold mb-2">Episode <%= @post.episode_number %></p>
            <% end %>
            <p class="text-emerald-100"><%= @post.reading_time %></p>
          </div>
          
          <!-- Audio Player -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <audio controls class="w-full">
              <source src="#" type="audio/mpeg">
              Your browser does not support the audio element.
            </audio>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Tags -->
    <% if @post.tags && @post.tags.any? %>
      <div class="mb-8 flex flex-wrap gap-2">
        <% @post.tags.each do |tag| %>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            #<%= tag %>
          </span>
        <% end %>
      </div>
    <% end %>

    <!-- Content -->
    <div class="prose prose-lg max-w-none">
      <%= render_markdown(@post.content) %>
    </div>

    <!-- Share Section -->
    <div class="mt-12 pt-8 border-t border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Share this article</h3>
      <div class="flex space-x-4">
        <button class="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
          </svg>
          <span>Twitter</span>
        </button>
        
        <button class="flex items-center space-x-2 px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
          </svg>
          <span>LinkedIn</span>
        </button>
        
        <button class="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m9.632 6.342c.202-.404.316-.86.316-1.342 0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684M8.684 7.658a3 3 0 110-2.684m9.632 2.684a3 3 0 110-2.684m0 0c.202.404.316.86.316 1.342 0 .482-.114.938-.316 1.342"/>
          </svg>
          <span>Copy Link</span>
        </button>
      </div>
    </div>
  </div>
</article>

<!-- Related Articles -->
<div class="py-16 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <h2 class="text-3xl font-bold text-gray-900 mb-8">Related Articles</h2>
    
    <div class="grid md:grid-cols-3 gap-8">
      <!-- Related Article 1 -->
      <article class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
        <div class="relative">
          <img src="https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Related article" 
               class="w-full h-48 object-cover">
        </div>
        
        <div class="p-6">
          <div class="flex items-center space-x-4 mb-3">
            <span class="text-purple-600 text-sm font-medium">Marketing Tips</span>
            <span class="text-gray-300">•</span>
            <span class="text-gray-500 text-sm">3 min read</span>
          </div>
          
          <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-purple-600 transition-colors">
            How to Measure Link Performance Effectively
          </h3>
          
          <p class="text-gray-600 text-sm mb-4">
            Learn the key metrics and KPIs you should track to understand your link marketing effectiveness.
          </p>
          
          <%= link_to "Read More →", "#", class: "text-purple-600 hover:text-purple-700 font-medium transition-colors" %>
        </div>
      </article>

      <!-- Related Article 2 -->
      <article class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
        <div class="relative">
          <img src="https://images.unsplash.com/photo-1553484771-047a44eee27b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Related article" 
               class="w-full h-48 object-cover">
        </div>
        
        <div class="p-6">
          <div class="flex items-center space-x-4 mb-3">
            <span class="text-emerald-600 text-sm font-medium">Tutorial</span>
            <span class="text-gray-300">•</span>
            <span class="text-gray-500 text-sm">5 min read</span>
          </div>
          
          <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-emerald-600 transition-colors">
            Setting Up Custom Domains for Your Links
          </h3>
          
          <p class="text-gray-600 text-sm mb-4">
            A step-by-step guide to configuring custom domains for better branding and trust.
          </p>
          
          <%= link_to "Read More →", "#", class: "text-purple-600 hover:text-purple-700 font-medium transition-colors" %>
        </div>
      </article>

      <!-- Related Article 3 -->
      <article class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
        <div class="relative">
          <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" 
               alt="Related article" 
               class="w-full h-48 object-cover">
        </div>
        
        <div class="p-6">
          <div class="flex items-center space-x-4 mb-3">
            <span class="text-blue-600 text-sm font-medium">Case Study</span>
            <span class="text-gray-300">•</span>
            <span class="text-gray-500 text-sm">7 min read</span>
          </div>
          
          <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
            How TechCorp Increased CTR by 300% with LinkMaster
          </h3>
          
          <p class="text-gray-600 text-sm mb-4">
            Real-world results from implementing advanced link management strategies.
          </p>
          
          <%= link_to "Read More →", "#", class: "text-purple-600 hover:text-purple-700 font-medium transition-colors" %>
        </div>
      </article>
    </div>
  </div>
</div>

<!-- Newsletter CTA -->
<div class="py-16 bg-gradient-to-br from-purple-900 to-pink-900">
  <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
    <h2 class="text-3xl font-bold text-white mb-4">Never Miss an Update</h2>
    <p class="text-xl text-purple-100 mb-8">
      Subscribe to our newsletter for the latest insights and product updates.
    </p>
    <form class="flex flex-col sm:flex-row gap-4 justify-center max-w-lg mx-auto">
      <input type="email" 
             placeholder="Enter your email" 
             required
             class="flex-1 px-4 py-3 border border-purple-300 rounded-xl focus:ring-2 focus:ring-white focus:border-transparent bg-white/10 backdrop-blur-sm text-white placeholder-purple-300">
      <button type="submit" class="bg-white text-purple-900 px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-200">
        Subscribe
      </button>
    </form>
  </div>
</div>