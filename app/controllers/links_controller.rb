class LinksController < ApplicationController
  include Pagy::Backend
  
  layout 'dashboard'
  
  before_action :authenticate_user!
  before_action :set_link, only: [:show, :edit, :update, :destroy, :archive, :unarchive, :qr_code]
  
  def index
    @pagy, @links = pagy(current_user.links
                                      .active
                                      .includes(:team)
                                      .search(params[:q])
                                      .order(created_at: :desc))
                         
    respond_to do |format|
      format.html
      format.turbo_stream
    end
  end
  
  def archived
    @pagy, @links = pagy(current_user.links
                                      .archived
                                      .includes(:team)
                                      .search(params[:q])
                                      .order(created_at: :desc))
    
    render :index
  end
  
  def show
    @recent_clicks = @link.link_clicks
                          .recent
                          .limit(10)
  end
  
  def new
    @link = current_user.links.build(original_url: params[:url])
    @teams = current_user.teams
    @custom_domains = current_user.custom_domains.verified.order(is_primary: :desc, domain: :asc)
  end
  
  def create
    @service = LinkShorteningService.new(user: current_user)
    result = @service.create_link(link_params)
    
    if result.success?
      @link = result.link
      respond_to do |format|
        format.html { redirect_to link_path(@link), notice: "Short link created successfully! Your link is ready to share." }
        format.turbo_stream { redirect_to link_path(@link), notice: "Short link created successfully! Your link is ready to share." }
      end
    else
      @link = current_user.links.build(link_params)
      @teams = current_user.teams
      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response(result.errors.values.flatten.first) }
      end
    end
  end
  
  def edit
    @teams = current_user.teams
    @custom_domains = current_user.custom_domains.verified.order(is_primary: :desc, domain: :asc)
  end
  
  def update
    # Handle custom_short_code separately
    update_params = link_params.except(:custom_short_code)
    
    if link_params[:custom_short_code].present? && link_params[:custom_short_code] != @link.short_code
      update_params[:short_code] = link_params[:custom_short_code]
    end
    
    if @link.update(update_params)
      respond_to do |format|
        format.html { redirect_to link_path(@link), notice: "Link updated successfully! Your changes have been saved." }
        format.turbo_stream { redirect_to link_path(@link), notice: "Link updated successfully! Your changes have been saved." }
      end
    else
      @teams = current_user.teams
      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.turbo_stream { render turbo_stream: turbo_stream_error_response(@link.errors.full_messages.first) }
      end
    end
  end
  
  def destroy
    @link.destroy!
    
    respond_to do |format|
      format.html { redirect_to links_url, notice: 'Link was successfully deleted.' }
      format.turbo_stream { render turbo_stream: turbo_stream_destroy_response }
    end
  end
  
  def archive
    @link.update!(is_archived: true)
    
    respond_to do |format|
      format.html { redirect_to links_url, notice: 'Link was archived.' }
      format.turbo_stream { render turbo_stream: turbo_stream_archive_response }
    end
  end
  
  def unarchive
    @link.update!(is_archived: false)
    
    respond_to do |format|
      format.html { redirect_to archived_links_url, notice: 'Link was unarchived.' }
      format.turbo_stream { render turbo_stream: turbo_stream_unarchive_response }
    end
  end
  
  def qr_code
    respond_to do |format|
      format.svg do
        send_data @link.qr_code_svg, 
                  type: 'image/svg+xml', 
                  disposition: 'inline', 
                  filename: "#{@link.short_code}_qr.svg"
      end
      
      format.png do
        send_data @link.qr_code_png.to_s, 
                  type: 'image/png', 
                  disposition: 'attachment', 
                  filename: "#{@link.short_code}_qr.png"
      end
    end
  end
  
  private
  
  def set_link
    @link = current_user.links.find(params[:id])
  end
  
  def link_params
    params.require(:link).permit(:original_url, :custom_short_code, :team_id, :custom_domain_id)
  end
  
  # Turbo Stream responses
  def turbo_stream_create_response
    turbo_stream.action(:redirect, target: "turbo-frame", url: links_path)
  end
  
  def turbo_stream_update_response
    [
      turbo_stream.replace(@link, partial: "links/link", locals: { link: @link }),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link updated successfully!" } })
    ]
  end
  
  def turbo_stream_destroy_response
    [
      turbo_stream.remove(@link),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link deleted successfully!" } })
    ]
  end
  
  def turbo_stream_archive_response
    [
      turbo_stream.remove(@link),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link archived!" } })
    ]
  end
  
  def turbo_stream_unarchive_response
    [
      turbo_stream.remove(@link),
      turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { notice: "Link unarchived!" } })
    ]
  end
  
  def turbo_stream_error_response(error)
    turbo_stream.update("flash", partial: "shared/flash", locals: { flash: { alert: error } })
  end
end