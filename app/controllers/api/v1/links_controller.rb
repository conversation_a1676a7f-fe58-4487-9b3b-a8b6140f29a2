module Api
  module V1
    class LinksController < BaseController
      include Pagy::Backend
      
      before_action :set_link, only: [:show, :update, :destroy, :archive, :unarchive, :stats]

      def index
        links = current_api_user.links.includes(:team, :link_clicks)
        links = apply_search(links)
        links = links.order(created_at: :desc)
        
        # Manual pagination for API
        page = (params[:page] || 1).to_i
        per_page = (params[:per_page] || 25).to_i
        per_page = [per_page, 100].min # Max 100 per page
        
        total = links.count
        offset = (page - 1) * per_page
        paginated_links = links.limit(per_page).offset(offset)
        
        render_success({
          links: serialize_links(paginated_links),
          meta: {
            current_page: page,
            per_page: per_page,
            total_pages: (total.to_f / per_page).ceil,
            total: total
          }
        })
      end

      def show
        render_success(serialize_link(@link))
      end

      def create
        service = LinkShorteningService.new(user: current_api_user)
        result = service.create_link(link_params)

        if result.success?
          render_success(serialize_link(result.link), status: :created)
        else
          render_error('Failed to create link', result.errors)
        end
      end

      def update
        service = LinkShorteningService.new(user: current_api_user)
        result = service.update_link(@link, link_params)

        if result.success?
          render_success(serialize_link(result.link))
        else
          render_error('Failed to update link', result.errors)
        end
      end

      def destroy
        @link.destroy
        head :no_content
      end

      def archive
        service = LinkShorteningService.new(user: current_api_user)
        result = service.archive_link(@link)

        if result.success?
          render_success(serialize_link(result.link))
        else
          render_error('Failed to archive link', result.errors)
        end
      end

      def unarchive
        @link.archived_at = nil
        
        if @link.save
          render_success(serialize_link(@link))
        else
          render_error('Failed to unarchive link', @link.errors.to_hash)
        end
      end

      def stats
        stats = {
          total_clicks: @link.link_clicks.count,
          unique_clicks: @link.unique_visitors,
          clicks_by_date: clicks_by_date,
          clicks_by_country: clicks_by_country,
          clicks_by_device: clicks_by_device,
          top_referrers: top_referrers,
          recent_clicks: recent_clicks
        }
        
        render_success(stats)
      end

      private

      def set_link
        @link = current_api_user.links.find_by(id: params[:id])
        render_not_found('Link') unless @link
      end

      def link_params
        params.require(:link).permit(:original_url, :custom_short_code, :team_id, :expires_at)
      end

      def apply_search(links)
        return links unless params[:q].present?
        
        links.where(
          'original_url ILIKE :query OR short_code ILIKE :query', 
          query: "%#{params[:q]}%"
        )
      end

      def serialize_link(link)
        {
          id: link.id,
          original_url: link.original_url,
          short_code: link.short_code,
          short_url: link.short_url,
          clicks_count: link.link_clicks_count,
          archived: link.is_archived,
          expires_at: link.expires_at,
          created_at: link.created_at,
          updated_at: link.updated_at
        }
      end

      def serialize_links(links)
        links.map { |link| serialize_link(link) }
      end

      def clicks_by_date
        # Use groupdate directly to avoid method conflict
        clicks = @link.link_clicks.where('clicked_at >= ?', 30.days.ago)
        
        # Group by day and count
        result = {}
        (0..29).each do |days_ago|
          date = days_ago.days.ago.to_date
          result[date.to_s] = clicks.where(clicked_at: date.all_day).count
        end
        result
      end

      def clicks_by_country
        @link.link_clicks
          .where("tracking_data->>'country_code' IS NOT NULL")
          .group("tracking_data->>'country_code'")
          .order('count_all DESC')
          .limit(10)
          .count
      end

      def clicks_by_device
        @link.link_clicks
          .group("tracking_data->>'device_type'")
          .count
      end

      def top_referrers
        @link.link_clicks
          .where("attribution_data->>'referrer' IS NOT NULL")
          .group("attribution_data->>'referrer'")
          .order('count_all DESC')
          .limit(10)
          .count
      end

      def recent_clicks
        @link.link_clicks
          .order(created_at: :desc)
          .limit(10)
          .map do |click|
            {
              created_at: click.created_at,
              country: click.country_code,
              city: click.city,
              device_type: click.device_type,
              browser: click.browser
            }
          end
      end
    end
  end
end