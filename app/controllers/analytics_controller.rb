require 'csv'

class AnalyticsController < ApplicationController
  include Pagy::Backend
  
  layout 'dashboard'
  
  before_action :authenticate_user!
  before_action :set_date_range
  before_action :set_link, only: [:show]

  def index
    @total_links = current_user.links.count
    @active_links = current_user.links.active.count
    @total_clicks = current_user.links.joins(:link_clicks).where(link_clicks: { clicked_at: @date_range }).count
    @unique_visitors = LinkClick.joins(:link).where(links: { user_id: current_user.id }, clicked_at: @date_range).distinct.count("tracking_data->>'ip_address'")
    
    # Top performing links
    @top_links = current_user.links
      .left_joins(:link_clicks)
      .where(link_clicks: { clicked_at: @date_range })
      .group('links.id')
      .order('COUNT(link_clicks.id) DESC')
      .limit(10)
      .select('links.*, COUNT(link_clicks.id) as clicks_in_period')
    
    # Click trends
    @clicks_by_date = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group_by_day(:clicked_at)
      .count
    
    # Device breakdown
    @clicks_by_device = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group("link_clicks.tracking_data->>'device_type'")
      .count
      .sort_by { |_, v| -v }
    
    # Geographic data
    @clicks_by_country = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .where("link_clicks.tracking_data->>'country_code' IS NOT NULL")
      .group("link_clicks.tracking_data->>'country_code'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
    
    # Referrer data
    @clicks_by_referrer = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .where("link_clicks.attribution_data->>'referrer' IS NOT NULL")
      .group("link_clicks.attribution_data->>'referrer'")
      .count
      .sort_by { |_, v| -v }
      .first(5)
    
    # NEW: Hourly distribution
    @clicks_by_hour = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group_by_hour_of_day(:clicked_at, format: "%l %P")
      .count
    
    # NEW: Day of week distribution
    raw_weekday_data = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group_by_day_of_week(:clicked_at, format: "%A")
      .count
    
    # Ensure days are in correct order
    weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    @clicks_by_day_of_week = weekday_order.index_with { |day| raw_weekday_data[day] || 0 }
    
    # NEW: Browser breakdown
    @clicks_by_browser = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group("link_clicks.tracking_data->>'browser'")
      .count
      .sort_by { |_, v| -v }
      .first(5)
    
    # NEW: OS breakdown
    @clicks_by_os = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group("link_clicks.tracking_data->>'os'")
      .count
      .sort_by { |_, v| -v }
      .first(5)
    
    # NEW: City breakdown (top cities)
    @clicks_by_city = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .where("link_clicks.tracking_data->>'city' IS NOT NULL")
      .group("link_clicks.tracking_data->>'city'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
    
    # NEW: Return visitor rate
    return_visitors = LinkClick
      .joins(:link)
      .where(links: { user_id: current_user.id }, clicked_at: @date_range)
      .group("tracking_data->>'ip_address'")
      .having("COUNT(*) > 1")
      .count
      .keys
      .count
    
    @return_visitor_rate = @unique_visitors > 0 ? (return_visitors.to_f / @unique_visitors * 100).round(2) : 0
    
    # NEW: Average clicks per visitor
    @avg_clicks_per_visitor = @unique_visitors > 0 ? (@total_clicks.to_f / @unique_visitors).round(2) : 0
    
    # NEW: Links created in period
    @new_links_count = current_user.links.where(created_at: @date_range).count
    
    # NEW: Growth rate (compare to previous period)
    previous_range = (@date_range.first - (@date_range.last - @date_range.first))..@date_range.first
    previous_clicks = current_user.links.joins(:link_clicks).where(link_clicks: { clicked_at: previous_range }).count
    @growth_rate = previous_clicks > 0 ? ((@total_clicks - previous_clicks).to_f / previous_clicks * 100).round(2) : 0
    
    respond_to do |format|
      format.html
      format.json { render json: {
        total_links: @total_links,
        total_clicks: @total_clicks,
        unique_visitors: @unique_visitors,
        clicks_by_date: @clicks_by_date,
        clicks_by_country: @clicks_by_country,
        clicks_by_device: @clicks_by_device
      }}
      format.any { render :index }
    end
  end

  def show
    @total_clicks = @link.link_clicks.where(clicked_at: @date_range).count
    @unique_visitors = @link.link_clicks.where(clicked_at: @date_range).distinct.count("tracking_data->>'ip_address'")
    
    # Click trends for this link
    @clicks_by_date = @link.link_clicks
      .where(clicked_at: @date_range)
      .group_by_day(:clicked_at)
      .count
    
    # Geographic data
    @clicks_by_country = @link.link_clicks
      .where(clicked_at: @date_range)
      .where("tracking_data->>'country_code' IS NOT NULL")
      .group("tracking_data->>'country_code'")
      .count
      .sort_by { |_, v| -v }
    
    # Device data
    @clicks_by_device = @link.link_clicks
      .where(clicked_at: @date_range)
      .group("tracking_data->>'device_type'")
      .count
    
    # Browser data
    @clicks_by_browser = @link.link_clicks
      .where(clicked_at: @date_range)
      .group("tracking_data->>'browser'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
    
    # OS data
    @clicks_by_os = @link.link_clicks
      .where(clicked_at: @date_range)
      .group("tracking_data->>'os'")
      .count
      .sort_by { |_, v| -v }
    
    # Referrer data
    @top_referrers = @link.link_clicks
      .where(clicked_at: @date_range)
      .where("attribution_data->>'referrer' IS NOT NULL")
      .group("attribution_data->>'referrer'")
      .count
      .sort_by { |_, v| -v }
      .first(10)
    
    # Recent clicks
    @recent_clicks = @link.link_clicks
      .where(clicked_at: @date_range)
      .order(clicked_at: :desc)
      .limit(50)
    
    # NEW: Hourly distribution for this link
    @clicks_by_hour = @link.link_clicks
      .where(clicked_at: @date_range)
      .group_by_hour_of_day(:clicked_at, format: "%l %P")
      .count
    
    # NEW: UTM campaign analysis
    @utm_campaigns = @link.link_clicks
      .where(clicked_at: @date_range)
      .where("attribution_data->>'utm_campaign' IS NOT NULL")
      .group("attribution_data->>'utm_campaign'")
      .count
      .sort_by { |_, v| -v }
    
    # NEW: UTM source analysis
    @utm_sources = @link.link_clicks
      .where(clicked_at: @date_range)
      .where("attribution_data->>'utm_source' IS NOT NULL")
      .group("attribution_data->>'utm_source'")
      .count
      .sort_by { |_, v| -v }
    
    # NEW: Click decay rate (clicks over time since creation)
    days_since_creation = [(Date.current - @link.created_at.to_date).to_i, 1].max
    @daily_average_clicks = (@link.link_clicks_count.to_f / days_since_creation).round(2)
    
    # NEW: Peak click hour for this link
    peak_hour = @clicks_by_hour.max_by { |_, v| v }
    @peak_hour = peak_hour ? peak_hour[0] : "N/A"
  end

  def export
    # Enhanced export with more data
    links_data = current_user.links.map do |link|
      clicks_in_range = link.link_clicks.where(clicked_at: @date_range).count
      unique_in_range = link.link_clicks.where(clicked_at: @date_range).distinct.count("tracking_data->>'ip_address'")
      
      {
        link: link.short_url,
        original_url: link.original_url,
        total_clicks: link.link_clicks_count,
        clicks_in_period: clicks_in_range,
        unique_visitors: link.unique_visitors,
        unique_in_period: unique_in_range,
        created_at: link.created_at.strftime('%Y-%m-%d'),
        last_clicked: link.link_clicks.maximum(:clicked_at)&.strftime('%Y-%m-%d %H:%M'),
        top_country: link.link_clicks
          .where(clicked_at: @date_range)
          .group("tracking_data->>'country_code'")
          .count
          .max_by { |_, v| v }&.first || "N/A",
        top_referrer: link.link_clicks
          .where(clicked_at: @date_range)
          .where("attribution_data->>'referrer' IS NOT NULL")
          .group("attribution_data->>'referrer'")
          .count
          .max_by { |_, v| v }&.first || "Direct"
      }
    end
    
    respond_to do |format|
      format.csv do
        send_data generate_csv(links_data), 
          filename: "analytics-export-#{Date.current}.csv",
          type: 'text/csv'
      end
    end
  end

  private

  def set_date_range
    if params[:date_range].present?
      case params[:date_range]
      when '7'
        @date_range = 7.days.ago..Time.current
        @date_range_label = 'Last 7 days'
      when '30'
        @date_range = 30.days.ago..Time.current
        @date_range_label = 'Last 30 days'
      when '90'
        @date_range = 90.days.ago..Time.current
        @date_range_label = 'Last 90 days'
      else
        @date_range = 30.days.ago..Time.current
        @date_range_label = 'Last 30 days'
      end
    elsif params[:start_date].present? && params[:end_date].present?
      @date_range = Date.parse(params[:start_date])..Date.parse(params[:end_date])
      @date_range_label = "#{params[:start_date]} - #{params[:end_date]}"
    else
      @date_range = 30.days.ago..Time.current
      @date_range_label = 'Last 30 days'
    end
  rescue ArgumentError
    @date_range = 30.days.ago..Time.current
    @date_range_label = 'Last 30 days'
  end

  def set_link
    @link = current_user.links.find_by(id: params[:id])
    render_not_found unless @link
  end

  def render_not_found
    respond_to do |format|
      format.html { render file: "#{Rails.root}/public/404.html", status: :not_found, layout: false }
      format.any { head :not_found }
    end
  end

  def generate_csv(data)
    CSV.generate(headers: true) do |csv|
      csv << ['Link', 'Original URL', 'Total Clicks', 'Clicks in Period', 'Unique Visitors', 'Unique in Period', 'Created', 'Last Clicked', 'Top Country', 'Top Referrer']
      
      data.each do |row|
        csv << [
          row[:link],
          row[:original_url],
          row[:total_clicks],
          row[:clicks_in_period],
          row[:unique_visitors],
          row[:unique_in_period],
          row[:created_at],
          row[:last_clicked],
          row[:top_country],
          row[:top_referrer]
        ]
      end
    end
  end
end