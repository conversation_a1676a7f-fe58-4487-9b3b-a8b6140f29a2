class RedirectsController < ApplicationController
  # No authentication required for public link redirects
  
  def show
    @link = Link.find_by(short_code: params[:short_code])
    
    if @link.nil?
      render "errors/not_found", status: :not_found
      return
    end
    
    if @link.expires_at && @link.expires_at < Time.current
      render "errors/gone", status: :gone
      return
    end
    
    # Track the click
    track_click
    
    # Redirect to the original URL
    redirect_to @link.original_url, allow_other_host: true
  end
  
  private
  
  def track_click
    # Skip tracking if Do Not Track is enabled
    do_not_track = request.headers['DNT'] == '1'
    
    # Extract UTM parameters
    utm_params = {
      utm_source: params[:utm_source],
      utm_medium: params[:utm_medium],
      utm_campaign: params[:utm_campaign],
      utm_term: params[:utm_term],
      utm_content: params[:utm_content]
    }.compact
    
    # Extract request information
    request_info = {
      request: request,
      do_not_track: do_not_track
    }
    
    # Use the AttributionTrackingService to track the click
    tracking_service = AttributionTrackingService.new(link: @link, request: request)
    tracking_service.track_click(utm_params)
  rescue => e
    # Log the error but don't prevent the redirect
    Rails.logger.error "Failed to track click: #{e.message}"
  end
end