module Settings
  class TeamSettingsController < ApplicationController
    before_action :authenticate_user!
    before_action :ensure_team_access
  
  def show
    @team = current_user_team
    @team_memberships = @team.team_memberships.includes(:user)
  end

  def update
    @team = current_user_team
    
    if @team.update(team_params)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("team-settings", partial: "team_settings/form", locals: { team: @team, success: true }) }
        format.html { redirect_to settings_team_settings_path, notice: 'Team settings updated successfully.' }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("team-settings", partial: "team_settings/form", locals: { team: @team }) }
        format.html { render :show, status: :unprocessable_entity }
      end
    end
  end

  private

  def current_user_team
    @current_user_team ||= current_user.teams.first || current_user.teams.build(name: "#{current_user.email}'s Team")
  end

  def ensure_team_access
    # For now, any authenticated user can access team settings
    # In production, you'd check if user is team admin/owner
    true
  end

  def team_params
    params.require(:team).permit(:name, :description, :default_domain)
  end
  end
end