class Settings::ApiTokensController < ApplicationController
  layout 'dashboard'
  before_action :authenticate_user!
  before_action :set_api_token, only: [:destroy]
  
  def index
    @api_tokens = current_user.api_tokens.active.order(created_at: :desc)
    @api_token = current_user.api_tokens.build
  end

  def create
    @api_token = current_user.api_tokens.build(api_token_params)
    
    if @api_token.save
      respond_to do |format|
        format.turbo_stream { render turbo_stream: [
          turbo_stream.prepend("api-tokens-list", partial: "settings/api_tokens/token", locals: { api_token: @api_token }),
          turbo_stream.replace("api-token-form", partial: "settings/api_tokens/form", locals: { api_token: current_user.api_tokens.build }),
          turbo_stream.replace("flash-messages", partial: "shared/flash", locals: { notice: "API token created successfully." })
        ] }
        format.html { redirect_to settings_api_tokens_path, notice: 'API token created successfully.' }
      end
    else
      @api_tokens = current_user.api_tokens.active.order(created_at: :desc)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("api-token-form", partial: "settings/api_tokens/form", locals: { api_token: @api_token }) }
        format.html { render :index, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @api_token.revoke!
    
    respond_to do |format|
      format.turbo_stream { render turbo_stream: [
        turbo_stream.remove("api-token-#{@api_token.id}"),
        turbo_stream.replace("flash-messages", partial: "shared/flash", locals: { notice: "API token revoked successfully." })
      ] }
      format.html { redirect_to settings_api_tokens_path, notice: 'API token revoked successfully.' }
    end
  end

  private

  def set_api_token
    @api_token = current_user.api_tokens.find(params[:id])
  end

  def api_token_params
    params.require(:api_token).permit(:name, :description)
  end
end