module Settings
  class IntegrationSettingsController < ApplicationController
    before_action :authenticate_user!
  
  def show
    @user = current_user
    @available_integrations = available_integrations
  end

  def update
    @user = current_user
    
    if @user.update(integration_params)
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("integration-settings", partial: "integration_settings/form", locals: { user: @user, success: true }) }
        format.html { redirect_to settings_integration_settings_path, notice: 'Integration settings updated successfully.' }
      end
    else
      respond_to do |format|
        format.turbo_stream { render turbo_stream: turbo_stream.replace("integration-settings", partial: "integration_settings/form", locals: { user: @user }) }
        format.html { render :show, status: :unprocessable_entity }
      end
    end
  end

  private

  def available_integrations
    [
      {
        id: 'google_analytics',
        name: 'Google Analytics',
        description: 'Track link clicks in Google Analytics',
        enabled: current_user.google_analytics_enabled || false,
        config_fields: ['tracking_id']
      },
      {
        id: 'facebook_pixel',
        name: 'Facebook Pixel',
        description: 'Track conversions with Facebook Pixel',
        enabled: current_user.facebook_pixel_enabled || false,
        config_fields: ['pixel_id']
      },
      {
        id: 'slack',
        name: 'Slack',
        description: 'Get notifications in Slack',
        enabled: current_user.slack_enabled || false,
        config_fields: ['webhook_url']
      },
      {
        id: 'zapier',
        name: 'Zapier',
        description: 'Connect with thousands of apps',
        enabled: current_user.zapier_enabled || false,
        config_fields: ['webhook_url']
      }
    ]
  end

  def integration_params
    params.require(:user).permit(
      :google_analytics_enabled, :google_analytics_tracking_id,
      :facebook_pixel_enabled, :facebook_pixel_id,
      :slack_enabled, :slack_webhook_url,
      :zapier_enabled, :zapier_webhook_url
    )
  end
  end
end