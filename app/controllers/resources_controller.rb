class ResourcesController < ApplicationController
  def blog
    @featured_post = BlogPost.featured.published.first
    @posts = BlogPost.published.where.not(id: @featured_post&.id)
  end

  def blog_post
    @post = BlogPost.find_by(slug: params[:id])
    redirect_to blog_path unless @post&.published?
  end

  def help_center
  end

  def guides
  end

  def webinars
  end

  def case_studies
  end
end
