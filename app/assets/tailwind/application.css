@import "tailwindcss";

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes blob {
  0% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -50px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
  100% { transform: translate(0px, 0px) scale(1); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 1px solid transparent;
}

.scrollbar-thumb-blue-300::-webkit-scrollbar-thumb {
  background-color: #93c5fd;
}

.scrollbar-track-blue-50::-webkit-scrollbar-track {
  background-color: #eff6ff;
}

.scrollbar-thumb-pink-300::-webkit-scrollbar-thumb {
  background-color: #f9a8d4;
}

.scrollbar-track-pink-50::-webkit-scrollbar-track {
  background-color: #fdf2f8;
}

.scrollbar-thumb-violet-300::-webkit-scrollbar-thumb {
  background-color: #c4b5fd;
}

.scrollbar-track-violet-50::-webkit-scrollbar-track {
  background-color: #f5f3ff;
}

.scrollbar-thumb-emerald-300::-webkit-scrollbar-thumb {
  background-color: #6ee7b7;
}

.scrollbar-track-emerald-50::-webkit-scrollbar-track {
  background-color: #ecfdf5;
}

.scrollbar-thumb-teal-300::-webkit-scrollbar-thumb {
  background-color: #5eead4;
}

.scrollbar-track-teal-50::-webkit-scrollbar-track {
  background-color: #f0fdfa;
}

.scrollbar-thumb-indigo-300::-webkit-scrollbar-thumb {
  background-color: #a5b4fc;
}

.scrollbar-track-indigo-50::-webkit-scrollbar-track {
  background-color: #eef2ff;
}