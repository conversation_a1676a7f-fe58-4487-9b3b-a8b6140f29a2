class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable
  
  # Associations
  has_many :links, dependent: :destroy
  has_many :team_memberships, dependent: :destroy
  has_many :teams, through: :team_memberships
  has_many :api_tokens, dependent: :destroy
  has_many :custom_domains, dependent: :destroy
  
  # Validations
  validates :first_name, :last_name, presence: true, if: :profile_completed?
  validates :timezone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name) }, allow_blank: true
  
  # Add the methods expected by tests (we'll implement fully later)
  def total_clicks
    # IMMEDIATE FIX: Bypass caching entirely until solid_cache schema is fixed
    # This prevents the byte_size and key_hash errors in the navbar
    calculate_total_clicks_directly
  end

  def member_of?(team)
    teams.exists?(id: team.id)
  end

  def can_create_link?
    # Placeholder - will implement with subscription service later
    true
  end

  def full_name
    return email if first_name.blank? && last_name.blank?
    "#{first_name} #{last_name}".strip
  end

  def profile_completed?
    first_name.present? && last_name.present?
  end

  def display_name
    profile_completed? ? full_name : email
  end

  def unique_visitors_count
    # Safely get unique visitors count with error handling
    begin
      links.joins(:link_clicks).distinct.count("link_clicks.tracking_data->>'ip_address'")
    rescue ActiveRecord::StatementInvalid => e
      Rails.logger.warn "Error calculating unique visitors: #{e.message}"
      0
    end
  end

  private

  def solid_cache_schema_ready?
    # Check if all required columns exist
    return false unless ActiveRecord::Base.connection.table_exists?('solid_cache_entries')

    columns = ActiveRecord::Base.connection.columns('solid_cache_entries').map(&:name)
    required_columns = %w[key value created_at key_hash byte_size]

    required_columns.all? { |col| columns.include?(col) }
  rescue => e
    Rails.logger.warn "Error checking solid_cache schema: #{e.message}"
    false
  end

  def calculate_total_clicks_directly
    links.joins(:link_clicks).count
  rescue => e
    Rails.logger.warn "Error calculating total clicks: #{e.message}"
    0
  end
end
