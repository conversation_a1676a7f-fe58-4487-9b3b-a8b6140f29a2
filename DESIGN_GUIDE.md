# LinkMaster Design Guide

This design guide documents the visual design system and patterns used throughout the LinkMaster application. It ensures consistency across all pages and provides a reference for future development.

## Core Design Principles

### Visual Language
- **Modern & Professional**: Clean, sophisticated interface with subtle depth
- **Consistent Interactions**: Predictable hover states and animations
- **Data-First Design**: Make analytics and metrics scannable and digestible
- **Responsive**: Mobile-first approach with desktop enhancements

### Color Palette

#### Primary Colors
- **Purple**: Primary brand color
  - `purple-500` to `purple-600` - Gradients
  - `purple-50` - Light backgrounds
  - `purple-100` - Hover states
  
#### Semantic Colors by Section
- **Blue**: Geographic/Location data (`blue-500` to `blue-600`)
- **Green**: Success/Growth metrics (`green-500` to `green-600`)
- **Amber/Orange**: Device/Browser analytics (`amber-500` to `amber-600`)
- **Pink/Rose**: Referrer/Traffic sources (`pink-500` to `pink-600`)
- **Violet/Purple**: Time-based/Recent activity (`violet-500` to `violet-600`)
- **Emerald/Teal**: Stats/Campaigns (`emerald-500` to `emerald-600`)
- **Indigo**: Performance/Top items (`indigo-500` to `indigo-600`)
- **Yellow**: Rates/Averages (`yellow-500` to `yellow-600`)

## Component Patterns

### Cards

#### Base Card Structure
```html
<div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-[color]-500/5 hover:-translate-y-1 transition-all duration-300">
  <!-- Content -->
</div>
```

#### Card Header Pattern
```html
<div class="flex items-center justify-between mb-6">
  <div>
    <h2 class="text-xl font-bold text-gray-900 mb-1">Title</h2>
    <p class="text-sm text-gray-600">Subtitle description</p>
  </div>
  <div class="w-12 h-12 bg-gradient-to-br from-[color]-500 to-[color]-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <!-- Icon -->
    </svg>
  </div>
</div>
```

### Stat Cards

#### Structure
```html
<div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-[color]-500/10 hover:-translate-y-1 transition-all duration-300">
  <div class="flex items-center justify-between mb-4">
    <div>
      <h3 class="text-sm font-semibold text-gray-600 mb-1">STAT LABEL</h3>
      <div class="w-8 h-1 bg-gradient-to-r from-[color]-500 to-[color]-400 rounded-full"></div>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-[color]-500 to-[color]-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
      <svg class="w-7 h-7 text-white"><!-- Icon --></svg>
    </div>
  </div>
  <div class="text-3xl font-bold text-gray-900 mb-1">123,456</div>
  <p class="text-sm text-gray-500">Context text</p>
</div>
```

### Chart Containers

```html
<div class="bg-gradient-to-br from-[color]-50/50 to-[color2]-50/50 rounded-xl p-4">
  <%= chart_helper %>
</div>
```

### Scrollable Sections

```html
<div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-[color]-300 scrollbar-track-[color]-50 pr-2">
  <div class="space-y-3">
    <!-- Scrollable content -->
  </div>
</div>
```

**Standard Heights**:
- Full lists: `max-h-80` (320px)
- Compact lists: `max-h-64` (256px)
- Mini lists: `max-h-32` (128px)

### Data Rows

#### Basic Row Pattern
```html
<div class="flex items-center justify-between p-3 bg-white/60 backdrop-blur rounded-lg hover:bg-white/80 transition-all duration-200">
  <div class="flex-1">
    <p class="text-sm font-medium text-gray-900">Primary text</p>
    <p class="text-xs text-gray-500">Secondary text</p>
  </div>
  <span class="text-sm font-bold text-gray-900 bg-[color]-100 px-3 py-1 rounded-full">
    Value
  </span>
</div>
```

### Badges & Pills

```html
<!-- Number badge -->
<span class="text-sm font-bold text-gray-900 bg-[color]-100 px-3 py-1 rounded-full">123</span>

<!-- Small badge -->
<span class="text-xs font-bold text-gray-900 bg-[color]-100 px-2 py-0.5 rounded-full">45</span>
```

## Typography

### Heading Hierarchy
- **Page Title**: Not shown in content (handled by layout)
- **Section Title**: `text-xl font-bold text-gray-900`
- **Card Title**: `text-lg font-bold text-gray-900`
- **Subsection**: `text-sm font-semibold text-gray-600`
- **Body Text**: `text-sm text-gray-700`
- **Secondary Text**: `text-xs text-gray-500`

### Stat Display
- **Large Numbers**: `text-3xl font-bold text-gray-900`
- **Medium Numbers**: `text-2xl font-semibold text-gray-900`
- **Small Numbers**: `text-sm font-bold text-gray-900`

## Spacing

### Padding
- **Page Container**: `p-6`
- **Cards**: `p-6`
- **Compact Cards**: `p-4`
- **Data Rows**: `p-3`
- **Small Items**: `p-2`

### Margins
- **Between Sections**: `mb-8`
- **Between Cards**: `gap-6`
- **Card Headers**: `mb-6`
- **Compact Headers**: `mb-4`
- **List Items**: `space-y-3`

### Border Radius
- **Cards**: `rounded-2xl`
- **Small Cards**: `rounded-xl`
- **Buttons/Inputs**: `rounded-lg`
- **Pills/Badges**: `rounded-full`

## Animations & Interactions

### Hover Effects
- **Cards**: `hover:shadow-lg hover:shadow-[color]-500/5 hover:-translate-y-1`
- **Icons**: `group-hover:scale-110` (on parent hover)
- **Buttons**: `hover:bg-[darker-shade]`
- **Rows**: `hover:bg-white/80`

### Transitions
- **Standard**: `transition-all duration-300`
- **Colors Only**: `transition-colors duration-200`
- **Transform**: `transition-transform duration-300`

### Progress Bars
```html
<div class="w-full bg-gray-200 rounded-full h-2">
  <div class="bg-gradient-to-r from-[color]-500 to-[color2]-500 h-2 rounded-full transition-all duration-500" 
       style="width: <%= percentage %>%"></div>
</div>
```

## Scrollbar Styling

Custom scrollbar classes defined in `app/assets/tailwind/application.css`:

```css
.scrollbar-thin { scrollbar-width: thin; }
.scrollbar-thin::-webkit-scrollbar { width: 6px; }
.scrollbar-thumb-[color]-300::-webkit-scrollbar-thumb { background-color: [color]; }
.scrollbar-track-[color]-50::-webkit-scrollbar-track { background-color: [color]; }
```

Available color variants:
- blue, pink, violet, emerald, teal, indigo

## Icon Guidelines

### Icon Sizes
- **Large Cards**: `w-6 h-6` in 12x12 container
- **Medium Cards**: `w-5 h-5` in 10x10 container
- **Small Icons**: `w-4 h-4` in 8x8 container
- **Stat Cards**: `w-7 h-7` in 12x12 container

### Icon Containers
```html
<!-- Standard gradient container -->
<div class="w-12 h-12 bg-gradient-to-br from-[color]-500 to-[color]-600 rounded-2xl flex items-center justify-center shadow-lg">
  <svg class="w-6 h-6 text-white"><!-- Icon --></svg>
</div>
```

## Best Practices

### Consistency Rules
1. Always use `rounded-2xl` for main cards
2. Maintain consistent color themes per data type
3. Use gradient backgrounds for chart containers
4. Apply hover effects to all interactive elements
5. Keep scrollable sections at predictable heights

### Accessibility
- Maintain color contrast ratios (WCAG AA)
- Use semantic HTML elements
- Provide hover and focus states
- Include screen reader text where needed

### Performance
- Limit animations to CSS transforms and opacity
- Use `backdrop-blur` sparingly
- Optimize scrollable content with virtual scrolling for large datasets

### Responsive Design
- Cards stack vertically on mobile
- Maintain touch-friendly tap targets (min 44px)
- Adjust font sizes for mobile readability
- Ensure horizontal scrolling is never required

## Example Implementation

```erb
<!-- Geographic Distribution Card -->
<div class="group bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-lg hover:shadow-blue-500/5 transition-all duration-300">
  <div class="flex items-center justify-between mb-6">
    <div>
      <h2 class="text-xl font-bold text-gray-900 mb-1">Geographic Distribution</h2>
      <p class="text-sm text-gray-600">Top countries by clicks</p>
    </div>
    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
  </div>
  
  <div class="bg-gradient-to-br from-blue-50/30 to-cyan-50/30 rounded-xl p-4">
    <div class="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-blue-50 pr-2">
      <div class="space-y-4">
        <!-- Country rows -->
      </div>
    </div>
  </div>
</div>
```

## Version History
- v1.0 - Initial design system documentation (July 2025)