# fly.toml app configuration file generated for linkmaster on 2025-07-27T13:50:33+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'linkmaster'
primary_region = 'lhr'
console_command = '/rails/bin/rails console'

[build]

[env]
  PORT = '8080'

[processes]
  app = './bin/rails server'
  solidq = 'bundle exec rake solid_queue:start'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

  [[http_service.checks]]
    interval = '10s'
    timeout = '2s'
    grace_period = '5s'
    method = 'GET'
    path = '/up'
    protocol = 'http'
    tls_skip_verify = false

    [http_service.checks.headers]
      X-Forwarded-Proto = 'https'

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
