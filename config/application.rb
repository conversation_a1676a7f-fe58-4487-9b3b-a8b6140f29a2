require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Linkmaster
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 8.0

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    
    # Solid Trifecta - No Redis needed!
    config.cache_store = :solid_cache_store
    config.active_job.queue_adapter = :solid_queue
    # ActionCable configuration for Rails 8 (Solid Cable)
    
    # Security defaults
    config.force_ssl = Rails.env.production?
    config.hosts.clear if Rails.env.development?
    
    # Docker build detection
    config.docker_build = ENV['DOCKER_BUILD'].present? || ENV['SECRET_KEY_BASE_DUMMY'].present?
    
    # Default host for URL generation (used by Link#short_url)
    config.default_host = ENV.fetch('DEFAULT_HOST', 'https://linkmaster.io')
  end
end
