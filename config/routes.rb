Rails.application.routes.draw do
  # Handle invalid format requests for Devise routes (must be before devise_for)
  get '/users/sign_up.:format', to: redirect('/users/sign_up'), constraints: { format: /(?!html).*/ }
  get '/users/sign_in.:format', to: redirect('/users/sign_in'), constraints: { format: /(?!html).*/ }
  
  # Devise routes for user authentication
  devise_for :users, defaults: { format: :html }
  
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Static pages
  get "about" => "pages#about"
  get "contact" => "pages#contact"
  post "contact" => "pages#create_contact"

  # Resources pages
  get "blog"           => "resources#blog"
  get "blog/:id"       => "resources#blog_post", as: :blog_post
  get "help"           => "resources#help_center"
  get "guides"         => "resources#guides"
  get "webinars"       => "resources#webinars"
  get "case-studies"   => "resources#case_studies", as: :case_studies
  get "request_demo"   => "resources#request_demo", as: :request_demo
  get "features"       => "resources#features", as: :features
  get "pricing"        => "resources#pricing", as: :pricing
  get "api_docs"       => "resources#api_docs", as: :api_docs
  get "integrations"   => "resources#integrations", as: :integrations
  get "changelog"      => "resources#changelog", as: :changelog
  get "careers"        => "resources#careers", as: :careers
  get "press"          => "resources#press", as: :press
  get "contact_sales"  => "resources#contact_sales", as: :contact_sales
  get "privacy"   => "resources#privacy", as: :privacy
  get "terms"     => "resources#terms", as: :terms
  get "security"  => "resources#security", as: :security
  get "gdpr"      => "resources#gdpr", as: :gdpr

  # Main application routes
  authenticated :user do
    root "dashboard#index", as: :authenticated_root
    
    get "dashboard" => "dashboard#index"
    
    # Settings routes
    resource :settings, only: [:show] do
      patch :update_account
      patch :update_notifications
      patch :update_security
      
      # Nested settings resources
      resource :team_settings, only: [:show, :update], path: 'team', controller: 'settings/team_settings'
      resources :api_tokens, only: [:index, :create, :destroy], path: 'api-tokens', controller: 'settings/api_tokens'
      resource :billing_settings, only: [:show, :update], path: 'billing', controller: 'settings/billing_settings'
      resources :custom_domains, only: [:index, :create, :destroy, :update], path: 'domains', controller: 'settings/custom_domains'
      resource :integration_settings, only: [:show, :update], path: 'integrations', controller: 'settings/integration_settings'
    end
  end
  
  root "landing_pages#index"
  
  resources :links do
    member do
      patch :archive
      patch :unarchive
      get :qr_code
    end
    collection do
      get :archived
    end
  end
  
  # Export route without format restriction (must be before resources :analytics)
  get '/analytics/export', to: 'analytics#export', as: :export_analytics
  
  resources :analytics, only: [:index, :show], defaults: { format: 'html' }
  
  # API routes
  namespace :api do
    namespace :v1 do
      resources :links do
        member do
          post :archive
          post :unarchive
          get :stats
        end
      end
    end
  end
  
  # Short link redirects (must be last to avoid conflicts)
  get "/:short_code", to: "redirects#show", as: :short_link
end
