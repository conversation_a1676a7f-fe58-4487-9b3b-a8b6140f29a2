FactoryBot.define do
  factory :custom_domain do
    user
    sequence(:domain) { |n| "example#{n}.com" }
    verified { false }
    verified_at { nil }
    verification_token { "linkmaster-verify-#{SecureRandom.hex(16)}" }
    is_primary { false }

    trait :verified do
      verified { true }
      verified_at { 1.day.ago }
    end

    trait :primary do
      verified { true }
      verified_at { 1.day.ago }
      is_primary { true }
    end
  end
end