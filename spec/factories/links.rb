FactoryBot.define do
  factory :link do
    user
    original_url { Faker::Internet.url }
    short_code { nil } # Let the model generate it
    metadata { {} }
    link_clicks_count { 0 }
    
    trait :with_custom_code do
      short_code { Faker::Internet.slug(words: nil, glue: '-') }
    end
    
    trait :with_metadata do
      metadata do
        {
          title: Faker::Company.catch_phrase,
          description: Faker::Lorem.paragraph,
          favicon_url: Faker::Company.logo,
          og_image_url: Faker::LoremFlickr.image
        }
      end
    end
    
    trait :with_team do
      association :team
    end
    
    trait :archived do
      archived_at { 1.day.ago }
    end
    
    trait :expired do
      expires_at { 1.day.ago }
    end
    
    trait :with_clicks do
      transient do
        clicks_count { 5 }
      end
      
      after(:create) do |link, evaluator|
        create_list(:link_click, evaluator.clicks_count, link: link)
        link.reload
      end
    end
  end
end