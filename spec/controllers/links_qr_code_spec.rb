require 'rails_helper'

RSpec.describe LinksController, 'QR code action', type: :controller do
  let(:user) { create(:user) }
  let(:link) { create(:link, user: user) }

  before do
    sign_in user
  end

  describe 'GET #qr_code' do
    context 'SVG format' do
      it 'returns an SVG QR code' do
        get :qr_code, params: { id: link.id, format: :svg }
        
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('image/svg+xml')
        expect(response.headers['Content-Disposition']).to include('inline')
        expect(response.headers['Content-Disposition']).to include("#{link.short_code}_qr.svg")
        expect(response.body).to include('<svg')
      end
    end

    context 'PNG format' do
      it 'returns a PNG QR code' do
        get :qr_code, params: { id: link.id, format: :png }
        
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include('image/png')
        expect(response.headers['Content-Disposition']).to include('attachment')
        expect(response.headers['Content-Disposition']).to include("#{link.short_code}_qr.png")
        expect(response.body).not_to be_empty
      end
    end

    context 'with another user\'s link' do
      let(:other_user) { create(:user) }
      let(:other_link) { create(:link, user: other_user) }

      it 'raises RecordNotFound' do
        expect {
          get :qr_code, params: { id: other_link.id, format: :png }
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end

    context 'when not authenticated' do
      before { sign_out user }

      it 'returns unauthorized status' do
        get :qr_code, params: { id: link.id, format: :png }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end