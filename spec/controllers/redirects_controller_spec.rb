require 'rails_helper'

RSpec.describe RedirectsController, type: :controller do
  let(:link) { create(:link) }
  
  describe "GET #show" do
    context "with valid short code" do
      it "redirects to the original URL" do
        get :show, params: { short_code: link.short_code }
        expect(response).to redirect_to(link.original_url)
      end
      
      it "tracks the click" do
        expect {
          get :show, params: { short_code: link.short_code }
        }.to change(LinkClick, :count).by(1)
      end
      
      it "tracks UTM parameters" do
        get :show, params: { 
          short_code: link.short_code,
          utm_source: "twitter",
          utm_medium: "social",
          utm_campaign: "spring_sale"
        }
        
        click = LinkClick.last
        expect(click.utm_source).to eq("twitter")
        expect(click.utm_medium).to eq("social")
        expect(click.utm_campaign).to eq("spring_sale")
      end
      
      it "tracks request information" do
        request.env['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'
        request.env['REMOTE_ADDR'] = '***********'
        
        get :show, params: { short_code: link.short_code }
        
        click = LinkClick.last
        expect(click.user_agent).to eq('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)')
        expect(click.ip_address).to be_present
      end
      
      it "respects Do Not Track header" do
        request.env['HTTP_DNT'] = '1'
        
        get :show, params: { short_code: link.short_code }
        
        click = LinkClick.last
        expect(click.ip_address).to be_nil
        expect(click.user_agent).to be_nil
      end
      
      it "updates link clicks counter cache" do
        expect {
          get :show, params: { short_code: link.short_code }
        }.to change { link.reload.link_clicks_count }.by(1)
      end
    end
    
    context "with invalid short code" do
      it "returns 404" do
        get :show, params: { short_code: "nonexistent" }
        expect(response).to have_http_status(:not_found)
      end
      
      it "renders a 404 page" do
        get :show, params: { short_code: "nonexistent" }
        expect(response).to render_template("errors/not_found")
      end
    end
    
    context "with expired link" do
      let(:expired_link) { create(:link, expires_at: 1.day.ago) }
      
      it "returns 410 Gone" do
        get :show, params: { short_code: expired_link.short_code }
        expect(response).to have_http_status(:gone)
      end
      
      it "does not track the click" do
        expect {
          get :show, params: { short_code: expired_link.short_code }
        }.not_to change(LinkClick, :count)
      end
    end
    
    context "with archived link" do
      let(:archived_link) { create(:link, is_archived: true) }
      
      it "still redirects" do
        get :show, params: { short_code: archived_link.short_code }
        expect(response).to redirect_to(archived_link.original_url)
      end
      
      it "tracks the click" do
        expect {
          get :show, params: { short_code: archived_link.short_code }
        }.to change(LinkClick, :count).by(1)
      end
    end
  end
end