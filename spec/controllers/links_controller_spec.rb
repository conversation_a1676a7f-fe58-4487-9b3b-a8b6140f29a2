require 'rails_helper'

RSpec.describe LinksController, type: :controller do
  let(:user) { create(:user) }
  let(:team) { create(:team) }
  let!(:team_membership) { create(:team_membership, user: user, team: team) }
  
  before { sign_in user }
  
  describe "GET #index" do
    let!(:active_link) { create(:link, user: user) }
    let!(:archived_link) { create(:link, user: user, is_archived: true) }
    let!(:other_user_link) { create(:link) }
    
    it "returns active links for the current user" do
      get :index
      expect(response).to be_successful
      expect(assigns(:links)).to include(active_link)
      expect(assigns(:links)).not_to include(archived_link)
      expect(assigns(:links)).not_to include(other_user_link)
    end
    
    it "paginates results" do
      create_list(:link, 30, user: user)
      get :index
      expect(assigns(:links).count).to eq(20) # pagy default items
      expect(assigns(:pagy)).to be_present
    end
    
    it "filters by search query" do
      matching_link = create(:link, user: user, original_url: "https://example.com/search-me")
      non_matching_link = create(:link, user: user, original_url: "https://other.com")
      
      get :index, params: { q: "search-me" }
      expect(assigns(:links)).to include(matching_link)
      expect(assigns(:links)).not_to include(non_matching_link)
    end
    
    it "responds to turbo stream requests" do
      get :index, format: :turbo_stream
      expect(response.media_type).to eq("text/vnd.turbo-stream.html")
    end
  end
  
  describe "GET #archived" do
    let!(:active_link) { create(:link, user: user) }
    let!(:archived_link) { create(:link, user: user, is_archived: true) }
    
    it "returns only archived links" do
      get :archived
      expect(response).to be_successful
      expect(assigns(:links)).to include(archived_link)
      expect(assigns(:links)).not_to include(active_link)
    end
  end
  
  describe "GET #show" do
    let(:link) { create(:link, user: user) }
    
    it "shows the link" do
      get :show, params: { id: link.id }
      expect(response).to be_successful
      expect(assigns(:link)).to eq(link)
    end
    
    it "returns 404 for other user's link" do
      other_link = create(:link)
      expect {
        get :show, params: { id: other_link.id }
      }.to raise_error(ActiveRecord::RecordNotFound)
    end
  end
  
  describe "GET #new" do
    it "renders the new link form" do
      get :new
      expect(response).to be_successful
      expect(assigns(:link)).to be_a_new(Link)
    end
    
    it "prefills URL from params" do
      get :new, params: { url: "https://example.com" }
      expect(assigns(:link).original_url).to eq("https://example.com")
    end
  end
  
  describe "POST #create" do
    context "with valid params" do
      let(:valid_params) {
        {
          link: {
            original_url: "https://example.com/article",
            custom_short_code: "my-article",
            team_id: team.id
          }
        }
      }
      
      it "creates a new link" do
        expect {
          post :create, params: valid_params
        }.to change(Link, :count).by(1)
        
        link = Link.last
        expect(link.user).to eq(user)
        expect(link.original_url).to eq("https://example.com/article")
        expect(link.short_code).to eq("my-article")
        expect(link.team).to eq(team)
      end
      
      it "redirects to the link page" do
        post :create, params: valid_params
        expect(response).to redirect_to(link_path(Link.last))
      end
      
      it "responds to turbo stream requests" do
        post :create, params: valid_params, format: :turbo_stream
        expect(response.media_type).to eq("text/vnd.turbo-stream.html")
      end
    end
    
    context "with invalid params" do
      let(:invalid_params) {
        {
          link: {
            original_url: "not-a-url"
          }
        }
      }
      
      it "does not create a link" do
        expect {
          post :create, params: invalid_params
        }.not_to change(Link, :count)
      end
      
      it "renders the new template" do
        post :create, params: invalid_params
        expect(response).to render_template(:new)
      end
    end
  end
  
  describe "GET #edit" do
    let(:link) { create(:link, user: user) }
    
    it "renders the edit form" do
      get :edit, params: { id: link.id }
      expect(response).to be_successful
      expect(assigns(:link)).to eq(link)
    end
  end
  
  describe "PATCH #update" do
    let(:link) { create(:link, user: user) }
    
    context "with valid params" do
      it "updates the link" do
        patch :update, params: {
          id: link.id,
          link: { custom_short_code: "updated-code" }
        }
        
        link.reload
        expect(link.short_code).to eq("updated-code")
        expect(response).to redirect_to(link_path(link))
      end
    end
    
    context "with invalid params" do
      it "does not update the link" do
        patch :update, params: {
          id: link.id,
          link: { original_url: "not-a-url" }
        }
        
        expect(response).to render_template(:edit)
      end
    end
  end
  
  describe "DELETE #destroy" do
    let!(:link) { create(:link, user: user) }
    
    it "destroys the link" do
      expect {
        delete :destroy, params: { id: link.id }
      }.to change(Link, :count).by(-1)
    end
    
    it "redirects to links index" do
      delete :destroy, params: { id: link.id }
      expect(response).to redirect_to(links_path)
    end
    
    it "responds to turbo stream requests" do
      delete :destroy, params: { id: link.id }, format: :turbo_stream
      expect(response.media_type).to eq("text/vnd.turbo-stream.html")
    end
  end
  
  describe "PATCH #archive" do
    let(:link) { create(:link, user: user) }
    
    it "archives the link" do
      patch :archive, params: { id: link.id }
      
      link.reload
      expect(link.is_archived).to be(true)
      expect(response).to redirect_to(links_path)
    end
    
    it "responds to turbo stream requests" do
      patch :archive, params: { id: link.id }, format: :turbo_stream
      expect(response.media_type).to eq("text/vnd.turbo-stream.html")
    end
  end
  
  describe "PATCH #unarchive" do
    let(:link) { create(:link, user: user, is_archived: true) }
    
    it "unarchives the link" do
      patch :unarchive, params: { id: link.id }
      
      link.reload
      expect(link.is_archived).to be(false)
      expect(response).to redirect_to(archived_links_path)
    end
  end
end