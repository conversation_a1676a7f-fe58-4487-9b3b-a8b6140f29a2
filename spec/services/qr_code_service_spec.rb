require 'rails_helper'

RSpec.describe QrCodeService do
  let(:link) { create(:link, short_code: 'abc123') }
  let(:service) { described_class.new(link) }

  describe '#initialize' do
    it 'sets the link and default options' do
      expect(service.link).to eq(link)
      expect(service.options).to include(
        size: 6,
        png_size: 300,
        color: '000',
        error_correction: :m,
        mode: nil,
        format: :svg
      )
    end

    it 'merges custom options with defaults' do
      custom_service = described_class.new(link, size: 10, color: 'fff')
      expect(custom_service.options[:size]).to eq(10)
      expect(custom_service.options[:color]).to eq('fff')
      expect(custom_service.options[:png_size]).to eq(300) # default preserved
    end
  end

  describe '#generate_svg' do
    it 'generates an SVG QR code' do
      svg = service.generate_svg
      expect(svg).to be_a(String)
      expect(svg).to include('<svg')
      expect(svg).to include('viewBox="0 0 400 400"')
      expect(svg).to include('class="qr-code-svg"')
    end

    it 'uses custom color option' do
      custom_service = described_class.new(link, color: 'ff0000')
      svg = custom_service.generate_svg
      expect(svg).to include('fill="#ff0000"')
    end
  end

  describe '#generate_png' do
    it 'generates a PNG QR code' do
      png = service.generate_png
      expect(png).to be_a(ChunkyPNG::Image)
      expect(png.width).to eq(300) # default png_size
      expect(png.height).to eq(300)
    end

    it 'uses custom size option' do
      custom_service = described_class.new(link, png_size: 500)
      png = custom_service.generate_png
      expect(png.width).to eq(500)
      expect(png.height).to eq(500)
    end
  end

  describe '#to_data_url' do
    it 'generates a data URL for the PNG' do
      data_url = service.to_data_url
      expect(data_url).to start_with('data:image/png;base64,')
      expect(data_url.length).to be > 100 # Should have actual data
    end
  end

  describe 'QR code content' do
    it 'encodes the short URL' do
      allow(link).to receive(:short_url).and_return('http://test.host/abc123')
      
      # Create a new instance to test the QR code content
      qr_service = described_class.new(link)
      qr_code = qr_service.send(:qr_code)
      
      expect(qr_code).to be_a(RQRCode::QRCode)
      # The QR code should contain the short URL
      expect(qr_code.qrcode.modules.size).to be > 0
    end
  end

  describe 'error correction levels' do
    %i[l m q h].each do |level|
      it "supports error correction level :#{level}" do
        service = described_class.new(link, error_correction: level)
        expect { service.generate_svg }.not_to raise_error
      end
    end
  end
end