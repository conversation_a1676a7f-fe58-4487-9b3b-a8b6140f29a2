require 'rails_helper'

RSpec.describe AttributionTrackingService do
  let(:link) { create(:link) }
  let(:request) { double('request') }
  let(:service) { described_class.new(link: link, request: request) }
  
  describe '#track_click' do
    context 'with valid request' do
      let(:ip) { '*******' }
      let(:user_agent) { 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36' }
      let(:referrer) { 'https://google.com' }
      
      before do
        allow(request).to receive(:remote_ip).and_return(ip)
        allow(request).to receive(:user_agent).and_return(user_agent)
        allow(request).to receive(:referrer).and_return(referrer)
        allow(request).to receive(:headers).and_return({})
      end
      
      it 'creates a link click record' do
        expect {
          service.track_click
        }.to change(LinkClick, :count).by(1)
      end
      
      it 'records basic tracking data' do
        result = service.track_click
        click = result.click
        
        expect(click.ip_address).to eq(ip)
        expect(click.user_agent).to eq(user_agent)
        expect(click.referrer).to eq(referrer)
        expect(click.clicked_at).to be_within(1.second).of(Time.current)
      end
      
      context 'with UTM parameters' do
        let(:utm_params) do
          {
            'utm_source' => 'newsletter',
            'utm_medium' => 'email',
            'utm_campaign' => 'summer_sale',
            'utm_term' => 'shoes',
            'utm_content' => 'header_cta'
          }
        end
        
        it 'extracts and stores UTM parameters' do
          result = service.track_click(utm_params)
          click = result.click
          
          expect(click.utm_source).to eq('newsletter')
          expect(click.utm_medium).to eq('email')
          expect(click.utm_campaign).to eq('summer_sale')
          expect(click.utm_term).to eq('shoes')
          expect(click.utm_content).to eq('header_cta')
        end
      end
      
      context 'with device detection' do
        context 'mobile device' do
          let(:user_agent) { 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' }
          
          it 'detects mobile device' do
            result = service.track_click
            expect(result.click.device_type).to eq('mobile')
          end
        end
        
        context 'tablet device' do
          let(:user_agent) { 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)' }
          
          it 'detects tablet device' do
            result = service.track_click
            expect(result.click.device_type).to eq('tablet')
          end
        end
        
        context 'desktop device' do
          let(:user_agent) { 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36' }
          
          it 'detects desktop device' do
            result = service.track_click
            expect(result.click.device_type).to eq('desktop')
          end
        end
      end
      
      context 'with bot detection' do
        context 'bot user agent' do
          let(:user_agent) { 'Googlebot/2.1 (+http://www.google.com/bot.html)' }
          
          it 'marks click as bot' do
            result = service.track_click
            expect(result.click.bot).to be true
          end
        end
        
        context 'regular user agent' do
          it 'marks click as not bot' do
            result = service.track_click
            expect(result.click.bot).to be false
          end
        end
      end
      
      context 'with browser detection' do
        it 'detects Chrome' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 Chrome/91.0.4472.124')
          result = service.track_click
          expect(result.click.browser).to eq('Chrome')
        end
        
        it 'detects Safari' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 Version/14.0 Safari/605.1.15')
          result = service.track_click
          expect(result.click.browser).to eq('Safari')
        end
        
        it 'detects Firefox' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 Firefox/89.0')
          result = service.track_click
          expect(result.click.browser).to eq('Firefox')
        end
      end
      
      context 'with OS detection' do
        it 'detects macOS' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)')
          result = service.track_click
          expect(result.click.os).to eq('macOS')
        end
        
        it 'detects Windows' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 (Windows NT 10.0; Win64; x64)')
          result = service.track_click
          expect(result.click.os).to eq('Windows')
        end
        
        it 'detects iOS' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)')
          result = service.track_click
          expect(result.click.os).to eq('iOS')
        end
        
        it 'detects Android' do
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0 (Linux; Android 11; Pixel 5)')
          result = service.track_click
          expect(result.click.os).to eq('Android')
        end
      end
    end
    
    context 'with geolocation' do
      let(:ip) { '*******' }
      
      before do
        allow(request).to receive(:remote_ip).and_return(ip)
        allow(request).to receive(:user_agent).and_return('Mozilla/5.0')
        allow(request).to receive(:referrer).and_return(nil)
        allow(request).to receive(:headers).and_return({})
      end
      
      it 'performs geolocation lookup' do
        expect(GeolocationService).to receive(:lookup).with(ip).and_return({
          country_code: 'US',
          city: 'Mountain View',
          region: 'California'
        })
        
        result = service.track_click
        click = result.click
        
        expect(click.country_code).to eq('US')
        expect(click.city).to eq('Mountain View')
        expect(click.region).to eq('California')
      end
      
      context 'when geolocation fails' do
        it 'continues without location data' do
          expect(GeolocationService).to receive(:lookup).and_raise(StandardError)
          
          result = service.track_click
          
          expect(result.success?).to be true
          expect(result.click.country_code).to be_nil
        end
      end
    end
    
    context 'with privacy compliance' do
      context 'GDPR mode' do
        before do
          allow(Rails.application.config).to receive(:respond_to?).and_return(true)
          allow(Rails.application.config).to receive(:gdpr_mode).and_return(true)
        end
        
        it 'anonymizes IP address' do
          allow(request).to receive(:remote_ip).and_return('*************')
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0')
          allow(request).to receive(:referrer).and_return(nil)
          allow(request).to receive(:headers).and_return({})
          
          result = service.track_click
          
          expect(result.click.ip_address).to eq('***********')
        end
      end
      
      context 'Do Not Track header' do
        before do
          allow(request).to receive(:headers).and_return({ 'DNT' => '1' })
          allow(request).to receive(:remote_ip).and_return('*******')
          allow(request).to receive(:user_agent).and_return('Mozilla/5.0')
          allow(request).to receive(:referrer).and_return(nil)
        end
        
        it 'respects DNT header' do
          result = service.track_click
          
          expect(result.click.ip_address).to be_nil
          expect(result.click.user_agent).to eq('DNT')
        end
      end
    end
  end
  
  describe '#original_url_with_attribution' do
    context 'with existing UTM parameters' do
      let(:link) { create(:link, original_url: 'https://example.com?utm_source=existing') }
      let(:utm_params) do
        {
          'utm_source' => 'new',
          'utm_medium' => 'email'
        }
      end
      
      it 'preserves existing parameters and adds new ones' do
        url = service.original_url_with_attribution(utm_params)
        
        # Parse the URL to check parameters properly
        uri = URI.parse(url)
        params = Rack::Utils.parse_query(uri.query)
        
        expect(params['utm_source']).to eq('new')  # New value should override
        expect(params['utm_medium']).to eq('email')
      end
    end
    
    context 'without existing parameters' do
      let(:link) { create(:link, original_url: 'https://example.com') }
      let(:utm_params) do
        {
          'utm_source' => 'newsletter',
          'utm_medium' => 'email'
        }
      end
      
      it 'adds UTM parameters' do
        url = service.original_url_with_attribution(utm_params)
        
        # Parse the URL to check parameters properly
        uri = URI.parse(url)
        params = Rack::Utils.parse_query(uri.query)
        
        expect(params['utm_source']).to eq('newsletter')
        expect(params['utm_medium']).to eq('email')
      end
    end
  end
end