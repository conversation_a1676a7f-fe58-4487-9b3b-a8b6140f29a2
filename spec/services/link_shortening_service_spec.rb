require 'rails_helper'

RSpec.describe LinkShorteningService do
  let(:user) { create(:user) }
  let(:service) { described_class.new(user: user) }
  
  describe '#create_link' do
    context 'with valid parameters' do
      let(:params) do
        {
          original_url: 'https://example.com/very/long/url',
          custom_short_code: nil,
          team_id: nil
        }
      end
      
      it 'creates a new link with generated short code' do
        result = service.create_link(params)
        
        expect(result.success?).to be true
        expect(result.link).to be_persisted
        expect(result.link.original_url).to eq(params[:original_url])
        expect(result.link.short_code).to match(/\A[a-zA-Z0-9]{6}\z/)
        expect(result.link.user).to eq(user)
      end
      
      it 'returns the short URL' do
        result = service.create_link(params)
        
        expect(result.short_url).to match(%r{/[a-zA-Z0-9]{6}$})
      end
    end
    
    context 'with custom short code' do
      let(:params) do
        {
          original_url: 'https://example.com',
          custom_short_code: 'my-custom-link',
          team_id: nil
        }
      end
      
      it 'creates link with custom short code' do
        result = service.create_link(params)
        
        expect(result.success?).to be true
        expect(result.link.short_code).to eq('my-custom-link')
      end
      
      context 'when custom code already exists' do
        before { create(:link, short_code: 'my-custom-link') }
        
        it 'returns an error' do
          result = service.create_link(params)
          
          expect(result.success?).to be false
          expect(result.errors[:short_code]).to include('has already been taken')
        end
      end
      
      context 'when custom code has invalid format' do
        let(:params) do
          {
            original_url: 'https://example.com',
            custom_short_code: 'invalid code!',
            team_id: nil
          }
        end
        
        it 'returns an error' do
          result = service.create_link(params)
          
          expect(result.success?).to be false
          expect(result.errors[:short_code]).to include('only allows letters, numbers, hyphens and underscores')
        end
      end
    end
    
    context 'with team assignment' do
      let(:team) { create(:team) }
      let(:params) do
        {
          original_url: 'https://example.com',
          custom_short_code: nil,
          team_id: team.id
        }
      end
      
      before do
        # Assuming user belongs to team
        team.users << user
      end
      
      it 'assigns link to team' do
        result = service.create_link(params)
        
        expect(result.success?).to be true
        expect(result.link.team).to eq(team)
      end
      
      context 'when user does not belong to team' do
        let(:other_team) { create(:team) }
        let(:params) do
          {
            original_url: 'https://example.com',
            custom_short_code: nil,
            team_id: other_team.id
          }
        end
        
        it 'returns an error' do
          result = service.create_link(params)
          
          expect(result.success?).to be false
          expect(result.errors[:team]).to include('not authorized')
        end
      end
    end
    
    context 'with invalid URL' do
      let(:params) do
        {
          original_url: 'not-a-valid-url',
          custom_short_code: nil,
          team_id: nil
        }
      end
      
      it 'returns an error' do
        result = service.create_link(params)
        
        expect(result.success?).to be false
        expect(result.errors[:original_url]).to include('is invalid')
      end
    end
    
    context 'with expiration date' do
      let(:params) do
        {
          original_url: 'https://example.com',
          custom_short_code: nil,
          team_id: nil,
          expires_at: 7.days.from_now
        }
      end
      
      it 'sets expiration date' do
        result = service.create_link(params)
        
        expect(result.success?).to be true
        expect(result.link.expires_at).to be_within(1.minute).of(7.days.from_now)
      end
    end
  end
  
  describe '#update_link' do
    let(:link) { create(:link, user: user) }
    
    context 'with valid parameters' do
      let(:params) do
        {
          original_url: 'https://updated-example.com',
          custom_short_code: 'new-code'
        }
      end
      
      it 'updates the link' do
        result = service.update_link(link, params)
        
        expect(result.success?).to be true
        expect(result.link.original_url).to eq('https://updated-example.com')
        expect(result.link.short_code).to eq('new-code')
      end
    end
    
    context 'when link belongs to another user' do
      let(:other_user) { create(:user) }
      let(:link) { create(:link, user: other_user) }
      let(:params) { { original_url: 'https://example.com' } }
      
      it 'returns an error' do
        result = service.update_link(link, params)
        
        expect(result.success?).to be false
        expect(result.errors[:base]).to include('not authorized')
      end
    end
  end
  
  describe '#archive_link' do
    let(:link) { create(:link, user: user) }
    
    it 'archives the link' do
      result = service.archive_link(link)
      
      expect(result.success?).to be true
      expect(result.link.archived_at).to be_present
    end
    
    context 'when link belongs to another user' do
      let(:other_user) { create(:user) }
      let(:link) { create(:link, user: other_user) }
      
      it 'returns an error' do
        result = service.archive_link(link)
        
        expect(result.success?).to be false
        expect(result.errors[:base]).to include('not authorized')
      end
    end
  end
  
  describe '#bulk_create_links' do
    let(:urls) do
      [
        'https://example1.com',
        'https://example2.com',
        'https://example3.com'
      ]
    end
    
    it 'creates multiple links' do
      result = service.bulk_create_links(urls)
      
      expect(result.success?).to be true
      expect(result.links.count).to eq(3)
      expect(result.links.map(&:original_url)).to match_array(urls)
    end
    
    context 'with some invalid URLs' do
      let(:urls) do
        [
          'https://example1.com',
          'invalid-url',
          'https://example3.com'
        ]
      end
      
      it 'creates valid links and reports errors' do
        result = service.bulk_create_links(urls)
        
        expect(result.success?).to be false
        expect(result.links.count).to eq(2)
        expect(result.errors['invalid-url']).to include('is invalid')
      end
    end
  end
end