require 'rails_helper'

RSpec.describe ApiAuthenticationService do
  let(:user) { create(:user) }
  let(:api_token) { create(:api_token, user: user) }
  let(:service) { described_class.new }

  describe '#authenticate' do
    context 'with valid token' do
      it 'returns the user and refreshes token' do
        result = service.authenticate(api_token.token)
        
        expect(result.success?).to be true
        expect(result.user).to eq(user)
        expect(api_token.reload.last_used_at).to be_present
      end
    end

    context 'with invalid token' do
      it 'returns failure' do
        result = service.authenticate('invalid_token')
        
        expect(result.failure?).to be true
        expect(result.error).to eq(:invalid_token)
        expect(result.message).to eq('Invalid or expired API token')
      end
    end

    context 'with revoked token' do
      let(:revoked_token) { create(:api_token, :revoked, user: user) }

      it 'returns failure' do
        result = service.authenticate(revoked_token.token)
        
        expect(result.failure?).to be true
        expect(result.error).to eq(:invalid_token)
        expect(result.message).to eq('Invalid or expired API token')
      end
    end

    context 'with nil token' do
      it 'returns failure' do
        result = service.authenticate(nil)
        
        expect(result.failure?).to be true
        expect(result.error).to eq(:missing_token)
        expect(result.message).to eq('Authorization header is missing')
      end
    end

    context 'with empty token' do
      it 'returns failure' do
        result = service.authenticate('')
        
        expect(result.failure?).to be true
        expect(result.error).to eq(:missing_token)
        expect(result.message).to eq('Authorization header is missing')
      end
    end
  end

  describe '#extract_token_from_header' do
    it 'extracts token from Bearer header' do
      header = "Bearer #{api_token.token}"
      token = service.extract_token_from_header(header)
      expect(token).to eq(api_token.token)
    end

    it 'returns nil for invalid format' do
      header = "Invalid #{api_token.token}"
      token = service.extract_token_from_header(header)
      expect(token).to be_nil
    end

    it 'returns nil for nil header' do
      token = service.extract_token_from_header(nil)
      expect(token).to be_nil
    end

    it 'handles case insensitive Bearer prefix' do
      header = "bearer #{api_token.token}"
      token = service.extract_token_from_header(header)
      expect(token).to eq(api_token.token)
    end
  end
end