require 'rails_helper'

RSpec.describe Link, 'QR code methods' do
  let(:link) { create(:link, short_code: 'test123') }

  describe '#qr_code_svg' do
    it 'generates an SVG QR code' do
      svg = link.qr_code_svg
      expect(svg).to be_a(String)
      expect(svg).to include('<svg')
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(format: :svg, size: 10)).and_call_original
      link.qr_code_svg(size: 10)
    end
  end

  describe '#qr_code_png' do
    it 'generates a PNG QR code' do
      png = link.qr_code_png
      expect(png).to be_a(ChunkyPNG::Image)
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(format: :png, png_size: 500)).and_call_original
      link.qr_code_png(png_size: 500)
    end
  end

  describe '#qr_code_data_url' do
    it 'generates a data URL' do
      data_url = link.qr_code_data_url
      expect(data_url).to start_with('data:image/png;base64,')
    end

    it 'passes options to QrCodeService' do
      expect(QrCodeService).to receive(:new).with(link, hash_including(color: 'ff0000')).and_call_original
      link.qr_code_data_url(color: 'ff0000')
    end
  end
end