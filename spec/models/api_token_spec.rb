require 'rails_helper'

RSpec.describe ApiToken, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    
    describe 'token validation' do
      it 'requires a token to be present' do
        api_token = build(:api_token)
        api_token.token = ''
        expect(api_token).not_to be_valid
        expect(api_token.errors[:token]).to include("can't be blank")
      end
    end
    
    describe 'uniqueness of token' do
      subject { build(:api_token) }
      it { should validate_uniqueness_of(:token) }
    end
  end

  describe 'scopes' do
    describe '.active' do
      let!(:active_token) { create(:api_token) }
      let!(:revoked_token) { create(:api_token, revoked_at: 1.day.ago) }

      it 'returns only non-revoked tokens' do
        expect(ApiToken.active).to include(active_token)
        expect(ApiToken.active).not_to include(revoked_token)
      end
    end
  end

  describe 'callbacks' do
    describe 'before_create' do
      it 'generates a token if not provided' do
        api_token = build(:api_token)
        api_token.token = nil
        expect { api_token.save! }.not_to raise_error
        expect(api_token.token).to be_present
        expect(api_token.token).to match(/^link_/)
      end

      it 'does not override token if provided' do
        custom_token = 'link_custom123'
        api_token = build(:api_token, token: custom_token)
        api_token.save
        expect(api_token.token).to eq(custom_token)
      end
    end
  end

  describe '#active?' do
    it 'returns true for non-revoked tokens' do
      token = create(:api_token)
      expect(token.active?).to be true
    end

    it 'returns false for revoked tokens' do
      token = create(:api_token, revoked_at: 1.hour.ago)
      expect(token.active?).to be false
    end
  end

  describe '#revoke!' do
    it 'sets revoked_at timestamp' do
      token = create(:api_token)
      expect(token.revoked_at).to be_nil
      
      token.revoke!
      expect(token.revoked_at).to be_present
    end
  end

  describe '#refresh!' do
    it 'updates last_used_at timestamp' do
      token = create(:api_token)
      old_timestamp = 1.day.ago
      token.update(last_used_at: old_timestamp)
      
      token.refresh!
      expect(token.last_used_at).to be > old_timestamp
    end
  end

  describe '.generate_token' do
    it 'generates a unique token with prefix' do
      token = ApiToken.generate_token
      expect(token).to match(/^link_[a-zA-Z0-9]{32}$/)
    end

    it 'generates different tokens each time' do
      token1 = ApiToken.generate_token
      token2 = ApiToken.generate_token
      expect(token1).not_to eq(token2)
    end
  end
end