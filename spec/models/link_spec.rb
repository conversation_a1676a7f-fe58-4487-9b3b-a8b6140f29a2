require 'rails_helper'

RSpec.describe Link, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:team).optional }
    it { should have_many(:link_clicks).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:original_url) }
    
    it 'validates uniqueness of short_code' do
      user = create(:user)
      create(:link, short_code: 'unique', user: user)
      duplicate = build(:link, short_code: 'unique', user: user)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:short_code]).to include('has already been taken')
    end
    
    it 'validates URL format' do
      link = build(:link, original_url: 'not-a-url')
      expect(link).not_to be_valid
      expect(link.errors[:original_url]).to include('is invalid')
    end
    
    it 'validates short code format' do
      link = build(:link, short_code: 'invalid code!')
      expect(link).not_to be_valid
      expect(link.errors[:short_code]).to include('only allows letters, numbers, hyphens and underscores')
    end
  end

  describe 'callbacks' do
    describe '#generate_short_code' do
      context 'when short_code is blank' do
        let(:link) { build(:link, short_code: nil) }
        
        it 'generates a 6-character alphanumeric code' do
          link.save!
          expect(link.short_code).to match(/\A[a-zA-Z0-9]{6}\z/)
        end
        
        it 'ensures uniqueness' do
          existing = create(:link, short_code: 'abc123')
          allow(SecureRandom).to receive(:alphanumeric).and_return('abc123', 'xyz789')
          
          link.save!
          expect(link.short_code).to eq('xyz789')
        end
      end
      
      context 'when custom short_code provided' do
        let(:link) { build(:link, short_code: 'custom') }
        
        it 'keeps the custom code' do
          link.save!
          expect(link.short_code).to eq('custom')
        end
      end
    end
    
    describe '#extract_metadata_later' do
      let(:link) { build(:link) }
      
      it 'enqueues metadata extraction job' do
        expect {
          link.save!
        }.to have_enqueued_job(MetadataExtractionJob).with(link.id)
      end
    end
  end

  describe '#short_url' do
    let(:link) { create(:link, short_code: 'abc123') }
    
    context 'with request object' do
      let(:request) { double(base_url: 'https://example.com') }
      
      it 'uses request base URL' do
        expect(link.short_url(request)).to eq('https://example.com/abc123')
      end
    end
    
    context 'without request object' do
      before do
        allow(Rails.application.config).to receive(:default_host).and_return('https://linkmaster.io')
      end
      
      it 'uses default host' do
        expect(link.short_url).to eq('https://linkmaster.io/abc123')
      end
    end
  end

  describe '#click_rate' do
    let(:link) { create(:link) }
    
    context 'with clicks' do
      before do
        create(:link_click, link: link, tracking_data: { ip_address: '*******' })
        create(:link_click, link: link, tracking_data: { ip_address: '*******' })
        create(:link_click, link: link, tracking_data: { ip_address: '*******' })
      end
      
      it 'calculates unique visitor percentage' do
        link.reload
        expect(link.click_rate).to eq(66.67)
      end
    end
    
    context 'without clicks' do
      it 'returns 0' do
        expect(link.click_rate).to eq(0)
      end
    end
  end
end