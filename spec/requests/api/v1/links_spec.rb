require 'rails_helper'

RSpec.describe 'Api::V1::Links', type: :request do
  let(:user) { create(:user) }
  let(:api_token) { create(:api_token, user: user) }
  let(:headers) { { 'Authorization' => "Bearer #{api_token.token}" } }
  
  describe 'GET /api/v1/links' do
    let!(:links) { create_list(:link, 3, user: user) }
    let!(:other_link) { create(:link) }

    context 'with valid authentication' do
      it 'returns user links' do
        get '/api/v1/links', headers: headers
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(json['data']['links'].size).to eq(3)
        expect(json['data']['meta']['total']).to eq(3)
      end

      it 'supports pagination' do
        # Create exactly 5 links to ensure we have enough for pagination
        create_list(:link, 2, user: user) # We already have 3 from let!, so total 5
        
        get '/api/v1/links', params: { page: 1, per_page: 2 }, headers: headers
        
        json = JSON.parse(response.body)
        expect(json['data']['links'].size).to eq(2)
        expect(json['data']['meta']['current_page']).to eq(1)
        expect(json['data']['meta']['per_page']).to eq(2)
        expect(json['data']['meta']['total']).to eq(5)
        
        # Test page 2
        get '/api/v1/links', params: { page: 2, per_page: 2 }, headers: headers
        json = JSON.parse(response.body)
        expect(json['data']['links'].size).to eq(2)
        expect(json['data']['meta']['current_page']).to eq(2)
      end

      it 'supports search' do
        searchable_link = create(:link, user: user, original_url: 'https://searchme.com')
        
        get '/api/v1/links', params: { q: 'searchme' }, headers: headers
        
        json = JSON.parse(response.body)
        expect(json['data']['links'].size).to eq(1)
        expect(json['data']['links'][0]['id']).to eq(searchable_link.id)
      end
    end

    context 'without authentication' do
      it 'returns unauthorized' do
        get '/api/v1/links'
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe 'GET /api/v1/links/:id' do
    let(:link) { create(:link, user: user) }

    context 'with valid authentication' do
      it 'returns the link' do
        get "/api/v1/links/#{link.id}", headers: headers
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(json['data']['id']).to eq(link.id)
        expect(json['data']['short_code']).to eq(link.short_code)
      end

      it 'returns 404 for non-existent link' do
        get '/api/v1/links/999999', headers: headers
        expect(response).to have_http_status(:not_found)
      end

      it 'returns 404 for other user link' do
        other_link = create(:link)
        get "/api/v1/links/#{other_link.id}", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'POST /api/v1/links' do
    let(:valid_params) do
      {
        link: {
          original_url: 'https://example.com',
          custom_short_code: 'custom123'
        }
      }
    end

    context 'with valid authentication' do
      it 'creates a new link' do
        expect {
          post '/api/v1/links', params: valid_params, headers: headers
        }.to change(Link, :count).by(1)
        
        expect(response).to have_http_status(:created)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(json['data']['original_url']).to eq('https://example.com')
        expect(json['data']['short_code']).to eq('custom123')
      end

      it 'returns errors for invalid data' do
        post '/api/v1/links', params: { link: { original_url: 'invalid' } }, headers: headers
        
        expect(response).to have_http_status(:unprocessable_entity)
        json = JSON.parse(response.body)
        expect(json['success']).to be false
        expect(json['errors']).to have_key('original_url')
      end
    end
  end

  describe 'PATCH /api/v1/links/:id' do
    let(:link) { create(:link, user: user) }
    let(:update_params) do
      {
        link: {
          original_url: 'https://updated.com'
        }
      }
    end

    context 'with valid authentication' do
      it 'updates the link' do
        patch "/api/v1/links/#{link.id}", params: update_params, headers: headers
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(json['data']['original_url']).to eq('https://updated.com')
      end

      it 'returns 404 for other user link' do
        other_link = create(:link)
        patch "/api/v1/links/#{other_link.id}", params: update_params, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'DELETE /api/v1/links/:id' do
    let!(:link) { create(:link, user: user) }

    context 'with valid authentication' do
      it 'deletes the link' do
        expect {
          delete "/api/v1/links/#{link.id}", headers: headers
        }.to change(Link, :count).by(-1)
        
        expect(response).to have_http_status(:no_content)
      end

      it 'returns 404 for other user link' do
        other_link = create(:link)
        delete "/api/v1/links/#{other_link.id}", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  describe 'POST /api/v1/links/:id/archive' do
    let(:link) { create(:link, user: user) }

    context 'with valid authentication' do
      it 'archives the link' do
        post "/api/v1/links/#{link.id}/archive", headers: headers
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(link.reload.archived_at).to be_present
      end
    end
  end

  describe 'POST /api/v1/links/:id/unarchive' do
    let(:link) { create(:link, :archived, user: user) }

    context 'with valid authentication' do
      it 'unarchives the link' do
        post "/api/v1/links/#{link.id}/unarchive", headers: headers
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(link.reload.archived_at).to be_nil
      end
    end
  end

  describe 'GET /api/v1/links/:id/stats' do
    let(:link) { create(:link, user: user) }
    let!(:clicks) { create_list(:link_click, 5, link: link) }

    context 'with valid authentication' do
      it 'returns link statistics' do
        get "/api/v1/links/#{link.id}/stats", headers: headers
        
        expect(response).to have_http_status(:ok)
        json = JSON.parse(response.body)
        expect(json['success']).to be true
        expect(json['data']['total_clicks']).to eq(5)
        expect(json['data']).to have_key('clicks_by_date')
        expect(json['data']).to have_key('clicks_by_country')
        expect(json['data']).to have_key('clicks_by_device')
      end
    end
  end
end